{% extends 'base.html.twig' %}

{% block title %}Contract Details{% endblock %}

{% block body %}
<div class="container-xxl position-relative bg-white d-flex p-0">
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Sidebar Start -->
    <div class="sidebar pe-4 pb-3">
        <nav class="navbar bg-light navbar-light">
            <a href="{{ path('rh_dashboard') }}" class="navbar-brand mx-4 mb-3">
                <h4 class="text-primary"><i class="fa fa-hashtag me-2"></i>RhManagement</h4>
            </a>
            <div class="d-flex align-items-center ms-4 mb-4">
                <div class="position-relative">
                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                    <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
                </div>
                <div class="ms-3">
                    <h6 class="mb-0">{{ app.user ? app.user.email : 'Employee' }}</h6>
                    {% set role = app.user ? app.user.roles[0] : null %}
                    <span class="small text-muted">
                        {% if role == 'ROLE_RH' %}
                            Human Resources
                        {% elseif role == 'ROLE_MANAGER' %}
                            Manager
                        {% elseif role == 'ROLE_EMPLOYE' %}
                            Employee
                        {% else %}
                            User
                        {% endif %}
                    </span>
                </div>
            </div>
            <div class="navbar-nav w-100">
                <a href="{{ path('rh_dashboard') }}" class="nav-item nav-link">
                    <i class="fa fa-tachometer-alt me-2"></i>Dashboard
                </a>
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fa fa-users me-2"></i>Employees
                    </a>
                    <div class="dropdown-menu bg-transparent border-0">
                        <a href="{{ path('app_user_new') }}" class="dropdown-item">
                            <i class="fa fa-user-plus me-2"></i>Add Employee
                        </a>
                        <a href="{{ path('app_user_index') }}" class="dropdown-item">
                            <i class="fa fa-list me-2"></i>Employee List
                        </a>
                    </div>
                </div>
                <a href="{{ path('app_contrat_index') }}" class="nav-item nav-link active">
                    <i class="fa fa-file-alt me-2"></i>Contracts
                </a>
                <a href="{{ path('app_leave_request_index') }}" class="nav-item nav-link">
                    <i class="fa fa-file-alt me-2"></i>Leave Requests</a>
                <a href="#" class="nav-item nav-link">
                    <i class="fa fa-cog me-2"></i>Settings
                </a>
            </div>
        </nav>
    </div>
    <!-- Sidebar End -->

    <!-- Content Start -->
    <div class="content">
        <!-- Navbar Start -->
        <nav class="navbar navbar-expand bg-light navbar-light sticky-top px-4 py-0">
            <a href="{{ path('rh_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
                <h2 class="text-primary mb-0"><i class="fa fa-hashtag"></i></h2>
            </a>
            <a href="#" class="sidebar-toggler flex-shrink-0">
                <i class="fa fa-bars"></i>
            </a>
            <form class="d-none d-md-flex ms-4">
                <input class="form-control border-0" type="search" placeholder="Search contracts..." aria-label="Search">
            </form>
            <div class="navbar-nav align-items-center ms-auto">
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-envelope me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Messages</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User" style="width: 40px; height: 40px;">
                                    <div class="ms-2">
                                        <h6 class="fw-normal mb-0">New HR message</h6>
                                        <small>15 minutes ago</small>
                                    </div>
                                </div>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item text-center">See all messages</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-bell me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Notifications</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <h6 class="fw-normal mb-0">New employee added</h6>
                                <small>1 hour ago</small>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item text-center">See all notifications</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <img class="rounded-circle me-lg-2" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                            <span class="d-none d-lg-inline-flex">{{ app.user ? app.user.email : 'Employee' }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">My Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <a href="{{ path('app_logout') }}" class="dropdown-item">Log Out</a>
                        </div>
                    </div>
                </div>
        </nav>
        <!-- Navbar End -->

        <div class="container mt-4 d-flex justify-content-center gap-2">
            <a href="{{ path('app_contrat_index') }}" class="btn btn-outline-secondary d-flex align-items-center">
                <i class="fa fa-arrow-left me-2"></i> Back to list
            </a>
            <button onclick="window.print()" class="btn btn-secondary">Print</button>
        </div>

       <div class="contract-document" id="contractDocument" style="margin: 0 auto; padding: 2rem; max-width: 700px;">
    <div class="contract-header text-center mb-4">
        <h2 class="mb-1">Employment Contract</h2>
        <div class="text-muted">Contract #{{ contrat.id }}</div>
    </div>
    <p>
        This Employment Contract is made between <strong>{{ contrat.user ? contrat.user.firstname ~ ' ' ~ contrat.user.lastname : 'the Employee' }}</strong>
        (email: {{ contrat.user ? contrat.user.email : '' }}) and <strong>RhManagement</strong>.
    </p>
    <p>
        <strong>Position:</strong> {{ contrat.position }}<br>
        <strong>Type of Contract:</strong> {{ contrat.type }}<br>
        <strong>Status:</strong> {{ contrat.status|capitalize }}
    </p>
    <p>
        The employment will commence on <strong>{{ contrat.dateD ? contrat.dateD|date('F j, Y') : '...' }}</strong>
        {% if contrat.dateF %}
            and will end on <strong>{{ contrat.dateF|date('F j, Y') }}</strong>.
        {% else %}
            and is of indefinite duration.
        {% endif %}
    </p>
    <p>
        The Employee will receive a salary of <strong>{{ contrat.salaire }} {{ contrat.devise is defined ? contrat.devise : 'TND' }}</strong> per month.
    </p>
    {% if contrat.description %}
        <p>
            <strong>Additional Terms:</strong><br>
            {{ contrat.description }}
        </p>
    {% endif %}
    <p>
        Both parties agree to the terms and conditions stated above.
    </p>
    <div class="row mt-5">
        <div class="col-6">
            <strong>Employer Signature:</strong>
            <div style="margin-top:40px;">____________________________</div>
        </div>
        <div class="col-6 text-end">
            <strong>Employee Signature:</strong>
            <div style="margin-top:40px;">____________________________</div>
        </div>
    </div>
</div>
    </div>
    <!-- Content End -->
</div>

<style>
@media print {
    body * {
        visibility: hidden !important;
    }
    .contract-document, .contract-document * {
        visibility: visible !important;
    }
    .contract-document {
        position: absolute;
        left: 0;
        top: 0;
        width: 100% !important;
        background: white;
        box-shadow: none;
        padding: 0;
        margin: 0;
    }
    .btn, .navbar, .sidebar, .container.mt-4 {
        display: none !important;
    }
}
</style>
{% endblock %}