<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250707211953 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE user ADD firstname VARCHAR(100) NOT NULL, ADD lastname VARCHAR(100) DEFAULT NULL, ADD poste VARCHAR(100) DEFAULT NULL, ADD date_embauche DATETIME DEFAULT NULL, ADD salaire_brut DOUBLE PRECISION DEFAULT NULL, ADD solde_conge DOUBLE PRECISION DEFAULT NULL, ADD date_fin_periode_essai DATETIME DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE user DROP firstname, DROP lastname, DROP poste, DROP date_embauche, DROP salaire_brut, DROP solde_conge, DROP date_fin_periode_essai
        SQL);
    }
}
