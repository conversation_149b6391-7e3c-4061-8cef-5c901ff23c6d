<?php

namespace App\Repository;

use App\Entity\LeaveRequest;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LeaveRequest>
 */
class LeaveRequestRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LeaveRequest::class);
    }

    public function findByEmployee(User $employee): array
    {
        return $this->createQueryBuilder('lr')
            ->andWhere('lr.employee = :employee')
            ->setParameter('employee', $employee)
            ->orderBy('lr.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findPendingForManager(User $manager): array
    {
        return $this->createQueryBuilder('lr')
            ->andWhere('lr.manager = :manager')
            ->andWhere('lr.status = :status')
            ->setParameter('manager', $manager)
            ->setParameter('status', LeaveRequest::STATUS_PENDING)
            ->orderBy('lr.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findPendingForHR(): array
    {
        return $this->createQueryBuilder('lr')
            ->andWhere('lr.status = :status')
            ->setParameter('status', LeaveRequest::STATUS_MANAGER_APPROVED)
            ->orderBy('lr.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findAllForManager(User $manager): array
    {
        return $this->createQueryBuilder('lr')
            ->andWhere('lr.manager = :manager')
            ->setParameter('manager', $manager)
            ->orderBy('lr.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findAllForHR(): array
    {
        return $this->createQueryBuilder('lr')
            ->orderBy('lr.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findApprovedByEmployeeAndDateRange(User $employee, \DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->createQueryBuilder('lr')
            ->andWhere('lr.employee = :employee')
            ->andWhere('lr.status = :status')
            ->andWhere('lr.startDate <= :endDate')
            ->andWhere('lr.endDate >= :startDate')
            ->setParameter('employee', $employee)
            ->setParameter('status', LeaveRequest::STATUS_HR_APPROVED)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getResult();
    }
}