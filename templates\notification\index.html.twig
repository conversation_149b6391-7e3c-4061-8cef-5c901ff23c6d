{% extends 'base.html.twig' %}

{% block title %}Notifications{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Notifications</h1>
                <button id="mark-all-read" class="btn btn-outline-primary">
                    <i class="fas fa-check-double"></i> Tout marquer comme lu
                </button>
            </div>

            {% if notifications is empty %}
                <div class="alert alert-info">
                    <i class="fas fa-bell"></i>
                    Aucune notification pour le moment.
                </div>
            {% else %}
                <div class="list-group">
                    {% for notification in notifications %}
                        <div class="list-group-item {{ not notification.isRead ? 'list-group-item-warning' : '' }}" 
                             data-notification-id="{{ notification.id }}">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    {% if not notification.isRead %}
                                        <i class="fas fa-circle text-primary" style="font-size: 8px;"></i>
                                    {% endif %}
                                    {{ notification.title }}
                                </h6>
                                <small>{{ notification.createdAt|date('d/m/Y H:i') }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message }}</p>
                            {% if notification.leaveRequest %}
                                <small>
                                    {% set actionUrl = path('app_leave_request_show', {id: notification.leaveRequest.id}) %}
                                    {% set actionText = 'Voir la demande' %}

                                    {# For managers: redirect to decision page if request is pending #}
                                    {% if app.user.hasRole('ROLE_MANAGER') and notification.leaveRequest.manager == app.user and notification.leaveRequest.isPending %}
                                        {% set actionUrl = path('app_leave_request_manager_decision', {id: notification.leaveRequest.id}) %}
                                        {% set actionText = 'Prendre une décision' %}
                                    {% endif %}

                                    {# For HR: redirect to decision page if request is manager approved #}
                                    {% if app.user.hasRole('ROLE_RH') and notification.leaveRequest.isManagerApproved %}
                                        {% set actionUrl = path('app_leave_request_hr_decision', {id: notification.leaveRequest.id}) %}
                                        {% set actionText = 'Décision finale RH' %}
                                    {% endif %}

                                    <a href="{{ actionUrl }}" class="text-decoration-none">
                                        <i class="fas fa-external-link-alt"></i> {{ actionText }}
                                    </a>
                                </small>
                            {% endif %}
                            {% if not notification.isRead %}
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                            data-notification-id="{{ notification.id }}">
                                        <i class="fas fa-check"></i> Marquer comme lu
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark single notification as read
    document.querySelectorAll('.mark-read-btn').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            
            fetch(`/notifications/${notificationId}/mark-read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const listItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
                    listItem.classList.remove('list-group-item-warning');
                    this.remove();
                    
                    // Remove the unread indicator
                    const indicator = listItem.querySelector('.fa-circle');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
        });
    });

    // Mark all notifications as read
    document.getElementById('mark-all-read').addEventListener('click', function() {
        fetch('/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    });
});
</script>
{% endblock %}
