{% extends 'base.html.twig' %}

{% block title %}Demandes en attente - Manager{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Demandes de congé en attente</h1>
                <a href="{{ path('app_leave_request_manager_all') }}" class="btn btn-outline-primary">
                    <i class="fas fa-list"></i> Toutes les demandes
                </a>
            </div>

            {% if pending_requests is empty %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Aucune demande de congé en attente de votre validation.
                </div>
            {% else %}
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Employé</th>
                                        <th>Type</th>
                                        <th><PERSON><PERSON><PERSON>de</th>
                                        <th>Jours</th>
                                        <th>So<PERSON>se le</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for request in pending_requests %}
                                        <tr>
                                            <td>{{ request.employee.fullName }}</td>
                                            <td>
                                                {% if request.type == 'annual' %}
                                                    <span class="badge bg-primary">Congé annuel</span>
                                                {% elseif request.type == 'sick' %}
                                                    <span class="badge bg-warning">Maladie</span>
                                                {% elseif request.type == 'maternity' %}
                                                    <span class="badge bg-info">Maternité</span>
                                                {% elseif request.type == 'paternity' %}
                                                    <span class="badge bg-info">Paternité</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Autre</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ request.startDate|date('d/m/Y') }} - {{ request.endDate|date('d/m/Y') }}
                                            </td>
                                            <td>{{ request.daysRequested }}</td>
                                            <td>{{ request.createdAt|date('d/m/Y H:i') }}</td>
                                            <td>
                                                <a href="{{ path('app_leave_request_show', {id: request.id}) }}"
                                                   class="btn btn-sm btn-info me-1"
                                                   title="Voir les détails de la demande">
                                                    <i class="fas fa-eye"></i> Voir
                                                </a>
                                                <a href="{{ path('app_leave_request_manager_decision', {id: request.id}) }}"
                                                   class="btn btn-sm btn-primary"
                                                   title="Prendre une décision sur cette demande">
                                                    <i class="fas fa-gavel"></i> Décider
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}