{% extends 'layout.html.twig' %}

{% block title %}Pending Requests - Manager{% endblock %}

{% block body %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Pending Leave Requests</h1>
            <a href="{{ path('app_leave_request_manager_all') }}" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> All Requests
            </a>
        </div>

        {% if pending_requests is empty %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                No leave requests pending your approval.
            </div>
        {% else %}
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Type</th>
                                    <th>Period</th>
                                    <th>Days</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                                <tbody>
                                    {% for request in pending_requests %}
                                        <tr>
                                            <td>{{ request.employee.fullName }}</td>
                                            <td>
                                                {% if request.type == 'annual' %}
                                                    <span class="badge bg-primary">Annual Leave</span>
                                                {% elseif request.type == 'sick' %}
                                                    <span class="badge bg-warning">Sick Leave</span>
                                                {% elseif request.type == 'maternity' %}
                                                    <span class="badge bg-info">Maternity</span>
                                                {% elseif request.type == 'paternity' %}
                                                    <span class="badge bg-info">Paternity</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Other</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ request.startDate|date('d/m/Y') }} - {{ request.endDate|date('d/m/Y') }}
                                            </td>
                                            <td>{{ request.daysRequested }}</td>
                                            <td>{{ request.createdAt|date('d/m/Y H:i') }}</td>
                                            <td>
                                                <a href="{{ path('app_leave_request_show', {id: request.id}) }}"
                                                   class="btn btn-sm btn-info me-1"
                                                   title="View request details">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="{{ path('app_leave_request_manager_decision', {id: request.id}) }}"
                                                   class="btn btn-sm btn-primary"
                                                   title="Make a decision on this request">
                                                    <i class="fas fa-gavel"></i> Decide
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}