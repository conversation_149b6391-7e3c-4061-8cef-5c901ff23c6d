<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>{% block title %}Employee Dashboard{% endblock %}</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="{{ asset('assets/img/favicon.ico') }}" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="{{ asset('assets/lib/owlcarousel/assets/owl.carousel.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/lib/tempusdominus/css/tempusdominus-bootstrap-4.min.css') }}" rel="stylesheet" />

    <!-- Customized Bootstrap Stylesheet -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="{{ asset('assets/css/style.css') }}" rel="stylesheet">
</head>

<body>
{% block body %}
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Sidebar Start -->
        <div class="sidebar pe-4 pb-3">
            <nav class="navbar bg-light navbar-light">
                <a href="{{ path('employee_dashboard') }}" class="navbar-brand mx-4 mb-3">
                    <h4 class="text-primary"><i class="fa fa-hashtag me-2"></i>RhManagement</h4>
                </a>
                <div class="d-flex align-items-center ms-4 mb-4">
                    <div class="position-relative">
                        <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                        <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
                    </div>
                    <div class="ms-3">
                        <h6 class="mb-0">{{ app.user ? app.user.email : 'Employee' }}</h6>
                        {% set role = app.user ? app.user.roles[0] : null %}
                        <span>    {% if role == 'ROLE_RH' %}        Human Resources
                            {% elseif role == 'ROLE_MANAGER' %}
                                Manager
                            {% elseif role == 'ROLE_EMPLOYEE' %}Employee
                            {% else %}        User    {% endif %}
                        </span>
                    </div>
                </div>

                <div class="navbar-nav w-100">
                    <a href="{{ path('employee_dashboard') }}" class="nav-item nav-link active">
                        <i class="fa fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-calendar me-2"></i>My Schedule</a>
                    <a href="{{ path('app_contrat_my') }}" class="nav-item nav-link">
                        <i class="fa fa-file-alt me-2"></i>My Contracts</a>
                    <a href="{{ path('app_leave_request_index') }}" class="nav-item nav-link">
                        <i class="fa fa-file-alt me-2"></i>Leave Requests</a>
                    <a href="" class="nav-item nav-link">
                        <i class="fa fa-user me-2"></i>My Profile</a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-envelope me-2"></i>Messages</a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-cog me-2"></i>Settings</a>
                </div>
            </nav>
        </div>
        <!-- Sidebar End -->

        <!-- Content Start -->
        <div class="content">
            <!-- Navbar Start -->
            <nav class="navbar navbar-expand bg-light navbar-light sticky-top px-4 py-0">
                <a href="{{ path('employee_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
                    <h2 class="text-primary mb-0"><i class="fa fa-hashtag"></i></h2>
                </a>
                <a href="#" class="sidebar-toggler flex-shrink-0">
                    <i class="fa fa-bars"></i>
                </a>
                <form class="d-none d-md-flex ms-4">
                    <input class="form-control border-0" type="search" placeholder="Search" aria-label="Search">
                </form>
                <div class="navbar-nav align-items-center ms-auto">
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-envelope me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Messages</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User" style="width: 40px; height: 40px;">
                                    <div class="ms-2">
                                        <h6 class="fw-normal mb-0">New schedule assigned</h6>
                                        <small>15 minutes ago</small>
                                    </div>
                                </div>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item">
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User" style="width: 40px; height: 40px;">
                                    <div class="ms-2">
                                        <h6 class="fw-normal mb-0">Meeting reminder</h6>
                                        <small>1 hour ago</small>
                                    </div>
                                </div>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item">
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User" style="width: 40px; height: 40px;">
                                    <div class="ms-2">
                                        <h6 class="fw-normal mb-0">Task assignment</h6>
                                        <small>3 hours ago</small>
                                    </div>
                                </div>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item text-center">See all messages</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-bell me-lg-2"></i>
                            {% if unread_count > 0 %}
                                <span class="badge bg-danger rounded-pill" id="notification-badge">{{ unread_count }}</span>
                            {% endif %}
                            <span class="d-none d-lg-inline-flex">Notifications</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0" style="min-width: 300px;">
                            {% if recent_notifications is empty %}
                                <div class="dropdown-item text-center text-muted">
                                    <i class="fas fa-bell-slash"></i>
                                    <br>Aucune notification
                                </div>
                            {% else %}
                                {% for notification in recent_notifications %}
                                    <a href="#" class="dropdown-item {{ not notification.isRead ? 'bg-warning bg-opacity-25' : '' }}"
                                       data-notification-id="{{ notification.id }}">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="fw-normal mb-0">
                                                    {% if not notification.isRead %}
                                                        <i class="fas fa-circle text-primary" style="font-size: 8px;"></i>
                                                    {% endif %}
                                                    {{ notification.title }}
                                                </h6>
                                                <small class="text-muted">{{ notification.message|length > 50 ? notification.message|slice(0, 50) ~ '...' : notification.message }}</small>
                                                <br>
                                                <small class="text-muted">{{ notification.createdAt|date('d/m/Y H:i') }}</small>
                                            </div>
                                            {% if not notification.isRead %}
                                                <button class="btn btn-sm btn-outline-primary mark-read-btn"
                                                        data-notification-id="{{ notification.id }}"
                                                        onclick="event.stopPropagation();">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </a>
                                    {% if not loop.last %}
                                        <hr class="dropdown-divider">
                                    {% endif %}
                                {% endfor %}
                                <hr class="dropdown-divider">
                            {% endif %}
                            <a href="{{ path('app_notification_index') }}" class="dropdown-item text-center">
                                <i class="fas fa-eye"></i> Voir toutes les notifications
                            </a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <img class="rounded-circle me-lg-2" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                            <span class="d-none d-lg-inline-flex">{{ app.user ? app.user.email : 'Employee' }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">My Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <a href="{{ path('app_logout') }}" class="dropdown-item">Log Out</a>
                        </div>
                    </div>
                </div>
            </nav>
            <!-- Navbar End -->

            <!-- Employee Stats Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="row g-4">
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-clock fa-3x text-primary"></i>
                            <div class="ms-3">
                                <p class="mb-2">Hours This Week</p>
                                <h6 class="mb-0">40h</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-calendar-check fa-3x text-success"></i>
                            <div class="ms-3">
                                <p class="mb-2">Days Present</p>
                                <h6 class="mb-0">22</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-calendar-times fa-3x text-warning"></i>
                            <div class="ms-3">
                                <p class="mb-2">Leave Days</p>
                                <h6 class="mb-0">5</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-tasks fa-3x text-info"></i>
                            <div class="ms-3">
                                <p class="mb-2">Tasks Completed</p>
                                <h6 class="mb-0">18</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Employee Stats End -->

            <!-- Quick Actions Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="row g-4">
                    <div class="col-sm-12 col-xl-6">
                        <div class="bg-light rounded p-4">
                            <h6 class="mb-4">Quick Actions</h6>
                            <div class="row g-3">
                                <div class="col-6">
                                    <button type="button" class="btn btn-primary w-100">
                                        <i class="fa fa-play me-2"></i>Clock In
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-danger w-100">
                                        <i class="fa fa-stop me-2"></i>Clock Out
                                    </button>
                                </div>
                                <div class="col-6">
                                    <a href="{{ path('app_leave_request_new') }}" class="btn btn-warning w-100">
                                        <i class="fa fa-calendar me-2"></i>Request Leave
                                    </a>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-info w-100">
                                        <i class="fa fa-file-alt me-2"></i>View Payslip
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-xl-6">
                        <div class="bg-light rounded p-4">
                            <h6 class="mb-4">Today's Schedule</h6>
                            <div class="d-flex align-items-center border-bottom py-3">
                                <div class="flex-shrink-0 btn-lg-square bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    9
                                </div>
                                <div class="w-100 ms-3">
                                    <h6 class="mb-0">Morning Meeting</h6>
                                    <small>9:00 AM - 10:00 AM</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center border-bottom py-3">
                                <div class="flex-shrink-0 btn-lg-square bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    2
                                </div>
                                <div class="w-100 ms-3">
                                    <h6 class="mb-0">Project Review</h6>
                                    <small>2:00 PM - 3:30 PM</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center pt-3">
                                <div class="flex-shrink-0 btn-lg-square bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    4
                                </div>
                                <div class="w-100 ms-3">
                                    <h6 class="mb-0">Team Training</h6>
                                    <small>4:00 PM - 5:00 PM</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Quick Actions End -->

            <!-- Recent Activity & Announcements Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="row g-4">
                    <div class="col-sm-12 col-xl-6">
                        <div class="bg-light rounded p-4">
                            <h6 class="mb-4">Recent Activity</h6>
                            <div class="d-flex align-items-center border-bottom py-3">
                                <i class="fa fa-clock text-primary me-3"></i>
                                <div class="w-100">
                                    <h6 class="mb-0">Clocked in at 9:00 AM</h6>
                                    <small class="text-muted">Today</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center border-bottom py-3">
                                <i class="fa fa-check text-success me-3"></i>
                                <div class="w-100">
                                    <h6 class="mb-0">Task completed: Update documentation</h6>
                                    <small class="text-muted">Yesterday</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center py-3">
                                <i class="fa fa-calendar text-warning me-3"></i>
                                <div class="w-100">
                                    <h6 class="mb-0">Leave request submitted</h6>
                                    <small class="text-muted">2 days ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-xl-6">
                        <div class="bg-light rounded p-4">
                            <h6 class="mb-4">Company Announcements</h6>
                            <div class="d-flex align-items-center border-bottom py-3">
                                <i class="fa fa-bullhorn text-primary me-3"></i>
                                <div class="w-100">
                                    <h6 class="mb-0">New HR Policy Updates</h6>
                                    <small class="text-muted">Posted 2 hours ago</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center border-bottom py-3">
                                <i class="fa fa-calendar text-success me-3"></i>
                                <div class="w-100">
                                    <h6 class="mb-0">Holiday Schedule Released</h6>
                                    <small class="text-muted">Posted yesterday</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center py-3">
                                <i class="fa fa-users text-info me-3"></i>
                                <div class="w-100">
                                    <h6 class="mb-0">Team Building Event</h6>
                                    <small class="text-muted">Posted 3 days ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Recent Activity & Announcements End -->

            <!-- Footer Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="bg-light rounded-top p-4">
                    <div class="row">
                        <div class="col-12 col-sm-6 text-center text-sm-start">
                            &copy; <a href="#">RhManagement</a>, All Right Reserved.
                        </div>
                        <div class="col-12 col-sm-6 text-center text-sm-end">
                            Employee Dashboard v1.0
                        </div>
                    </div>
                </div>
            </div>
            <!-- Footer End -->
        </div>
        <!-- Content End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>
{% endblock %}

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ asset('assets/lib/easing/easing.min.js') }}"></script>
<script src="{{ asset('assets/lib/waypoints/waypoints.min.js') }}"></script>
<script src="{{ asset('assets/lib/owlcarousel/owl.carousel.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/moment.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/moment-timezone.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/tempusdominus-bootstrap-4.min.js') }}"></script>

<!-- Template Javascript -->
<script src="{{ asset('assets/js/main.js') }}"></script>

<script>
// Check for new notifications every 30 seconds
function checkNotifications() {
    fetch('/notifications/unread-count', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const notificationBadge = document.getElementById('notification-badge');
        if (data.count > 0) {
            if (notificationBadge) {
                notificationBadge.textContent = data.count;
                notificationBadge.style.display = 'inline-block';
            } else {
                // Create badge if it doesn't exist
                const bellIcon = document.querySelector('.fa-bell');
                if (bellIcon) {
                    const badge = document.createElement('span');
                    badge.id = 'notification-badge';
                    badge.className = 'badge bg-danger rounded-pill';
                    badge.textContent = data.count;
                    bellIcon.parentNode.insertBefore(badge, bellIcon.nextSibling);
                }
            }
        } else {
            if (notificationBadge) {
                notificationBadge.style.display = 'none';
            }
        }
    })
    .catch(error => console.error('Error checking notifications:', error));
}

// Mark single notification as read
document.addEventListener('click', function(e) {
    if (e.target.closest('.mark-read-btn')) {
        e.preventDefault();
        e.stopPropagation();

        const button = e.target.closest('.mark-read-btn');
        const notificationId = button.dataset.notificationId;

        fetch(`/notifications/${notificationId}/mark-read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dropdownItem = button.closest('.dropdown-item');
                dropdownItem.classList.remove('bg-warning', 'bg-opacity-25');
                button.remove();

                // Remove the unread indicator
                const indicator = dropdownItem.querySelector('.fa-circle');
                if (indicator) {
                    indicator.remove();
                }

                // Update notification count
                checkNotifications();
            }
        })
        .catch(error => console.error('Error marking notification as read:', error));
    }
});

// Initial check and then every 30 seconds
document.addEventListener('DOMContentLoaded', function() {
    checkNotifications();
    setInterval(checkNotifications, 30000);
});
</script>
</body>

</html>