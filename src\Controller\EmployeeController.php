<?php

namespace App\Controller;

use App\Repository\NotificationRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class EmployeeController extends AbstractController
{
    public function __construct(
        private NotificationRepository $notificationRepository
    ) {}

    #[Route('/employee/dashboard', name: 'employee_dashboard')]
    public function dashboard(): Response
    {
        $user = $this->getUser();
        $recentNotifications = [];
        $unreadCount = 0;

        if ($user) {
            $recentNotifications = $this->notificationRepository->findRecentByRecipient($user, 5);
            $unreadCount = $this->notificationRepository->countUnreadByRecipient($user);
        }

        return $this->render('employee/dashboard.html.twig', [
            'recent_notifications' => $recentNotifications,
            'unread_count' => $unreadCount,
        ]);
    }
    #[Route('/employee/rh_dashboard', name: 'rh_dashboard')]
    public function Rhdashboard(): Response
    {
        $user = $this->getUser();
        $recentNotifications = [];
        $unreadCount = 0;

        if ($user) {
            $recentNotifications = $this->notificationRepository->findRecentByRecipient($user, 5);
            $unreadCount = $this->notificationRepository->countUnreadByRecipient($user);
        }

        return $this->render('employee/rh_dashboard.html.twig', [
            'recent_notifications' => $recentNotifications,
            'unread_count' => $unreadCount,
        ]);
    }

    #[Route('/employee/manager_dashboard', name: 'manager_dashboard')]
    public function Managerdashboard(): Response
    {
        $user = $this->getUser();
        $recentNotifications = [];
        $unreadCount = 0;

        if ($user) {
            $recentNotifications = $this->notificationRepository->findRecentByRecipient($user, 5);
            $unreadCount = $this->notificationRepository->countUnreadByRecipient($user);
        }

        return $this->render('employee/manager_dashboard.html.twig', [
            'recent_notifications' => $recentNotifications,
            'unread_count' => $unreadCount,
        ]);
    }
}
