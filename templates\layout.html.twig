<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>{% block title %}HR Management{% endblock %}</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="{{ asset('assets/img/favicon.ico') }}" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="{{ asset('assets/lib/owlcarousel/assets/owl.carousel.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/lib/tempusdominus/css/tempusdominus-bootstrap-4.min.css') }}" rel="stylesheet" />

    <!-- Customized Bootstrap Stylesheet -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="{{ asset('assets/css/style.css') }}" rel="stylesheet">
    
    {% block stylesheets %}{% endblock %}
</head>

<body>
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Sidebar Start -->
        <div class="sidebar pe-4 pb-3">
            <nav class="navbar bg-light navbar-light">
                <a href="{{ path('employee_dashboard') }}" class="navbar-brand mx-4 mb-3">
                    <h4 class="text-primary"><i class="fa fa-hashtag me-2"></i>HR Management</h4>
                </a>
                <div class="d-flex align-items-center ms-4 mb-4">
                    <div class="position-relative">
                        <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                        <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
                    </div>
                    <div class="ms-3">
                        <h6 class="mb-0">{{ app.user ? app.user.email : 'User' }}</h6>
                        {% set role = app.user ? app.user.roles[0] : null %}
                        <span>
                            {% if role == 'ROLE_RH' %}
                                Human Resources
                            {% elseif role == 'ROLE_MANAGER' %}
                                Manager
                            {% elseif role == 'ROLE_EMPLOYE' %}
                                Employee
                            {% else %}
                                User
                            {% endif %}
                        </span>
                    </div>
                </div>

                <div class="navbar-nav w-100">
                    <a href="{{ path('employee_dashboard') }}" class="nav-item nav-link">
                        <i class="fa fa-tachometer-alt me-2"></i>Dashboard</a>
                    
                    {% if app.user.hasRole('ROLE_RH') %}
                        <div class="nav-item dropdown">
                            <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fa fa-user me-2"></i>Employees
                            </a>
                            <div class="dropdown-menu bg-transparent border-0 text-center">
                                <a href="{{ path('app_user_new') }}" class="dropdown-item small py-2">
                                    <i class="fa fa-user-plus me-1"></i> Add Employee
                                </a>
                                <a href="{{ path('app_user_index') }}" class="dropdown-item small py-2">
                                    <i class="fa fa-users me-1"></i> Employee List
                                </a>
                            </div>
                        </div>
                        <a href="{{ path('app_leave_request_hr_pending') }}" class="nav-item nav-link">
                            <i class="fa fa-file-alt me-2"></i>Leave Requests</a>
                    {% elseif app.user.hasRole('ROLE_MANAGER') %}
                        <a href="{{ path('app_leave_request_manager_pending') }}" class="nav-item nav-link">
                            <i class="fa fa-file-alt me-2"></i>Leave Requests</a>
                    {% else %}
                        <a href="{{ path('app_leave_request_index') }}" class="nav-item nav-link">
                            <i class="fa fa-file-alt me-2"></i>My Leave Requests</a>
                        <a href="{{ path('app_leave_request_new') }}" class="nav-item nav-link">
                            <i class="fa fa-plus me-2"></i>New Leave Request</a>
                    {% endif %}
                    
                    <a href="{{ path('app_notification_index') }}" class="nav-item nav-link">
                        <i class="fa fa-bell me-2"></i>Notifications</a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-user me-2"></i>My Profile</a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-cog me-2"></i>Settings</a>
                </div>
            </nav>
        </div>
        <!-- Sidebar End -->

        <!-- Content Start -->
        <div class="content">
            <!-- Navbar Start -->
            <nav class="navbar navbar-expand bg-light navbar-light sticky-top px-4 py-0">
                <a href="{{ path('employee_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
                    <h2 class="text-primary mb-0"><i class="fa fa-hashtag"></i></h2>
                </a>
                <a href="#" class="sidebar-toggler flex-shrink-0">
                    <i class="fa fa-bars"></i>
                </a>
                <form class="d-none d-md-flex ms-4">
                    <input class="form-control border-0" type="search" placeholder="Search" aria-label="Search">
                </form>
                <div class="navbar-nav align-items-center ms-auto">
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-envelope me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Messages</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item text-center">No new messages</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-bell me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Notifications</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0" style="min-width: 300px;">
                            <a href="{{ path('app_notification_index') }}" class="dropdown-item text-center">
                                <i class="fas fa-eye"></i> View All Notifications
                            </a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <img class="rounded-circle me-lg-2" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                            <span class="d-none d-lg-inline-flex">{{ app.user ? app.user.email : 'User' }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">My Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <a href="{{ path('app_logout') }}" class="dropdown-item">Log Out</a>
                        </div>
                    </div>
                </div>
            </nav>
            <!-- Navbar End -->

            <!-- Main Content -->
            <div class="container-fluid pt-4 px-4">
                {% for label, messages in app.flashes %}
                    {% for message in messages %}
                        <div class="alert alert-{{ label == 'error' ? 'danger' : label }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endfor %}
                
                {% block body %}{% endblock %}
            </div>

            <!-- Footer Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="bg-light rounded-top p-4">
                    <div class="row">
                        <div class="col-12 col-sm-6 text-center text-sm-start">
                            &copy; <a href="#">HR Management</a>, All Right Reserved.
                        </div>
                        <div class="col-12 col-sm-6 text-center text-sm-end">
                            HR Management System v1.0
                        </div>
                    </div>
                </div>
            </div>
            <!-- Footer End -->
        </div>
        <!-- Content End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ asset('assets/lib/easing/easing.min.js') }}"></script>
<script src="{{ asset('assets/lib/waypoints/waypoints.min.js') }}"></script>
<script src="{{ asset('assets/lib/owlcarousel/owl.carousel.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/moment.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/moment-timezone.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/tempusdominus-bootstrap-4.min.js') }}"></script>

<!-- Template Javascript -->
<script src="{{ asset('assets/js/main.js') }}"></script>

{% block javascripts %}{% endblock %}
</body>

</html>
