<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250707190736 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE user ADD poste VARCHAR(255) DEFAULT NULL, ADD date_embauche DATE DEFAULT NULL, ADD salaire_brut DOUBLE PRECISION DEFAULT NULL, ADD solde_conge DOUBLE PRECISION DEFAULT NULL, ADD date_fin_periode_essai DATE DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE user DROP poste, DROP date_embauche, DROP salaire_brut, DROP solde_conge, DROP date_fin_periode_essai
        SQL);
    }
}
