{% extends 'layout.html.twig' %}

{% block title %}All Requests - HR{% endblock %}

{% block body %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>All Leave Requests</h1>
            <a href="{{ path('app_leave_request_hr_pending') }}" class="btn btn-outline-primary">
                <i class="fas fa-clock"></i> Pending Only
            </a>
        </div>

        {% if all_requests is empty %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                No leave requests found.
            </div>
        {% else %}
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Manager</th>
                                    <th>Type</th>
                                    <th>Period</th>
                                    <th>Days</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in all_requests %}
                                    <tr>
                                        <td>{{ request.employee.fullName }}</td>
                                        <td>{{ request.manager ? request.manager.fullName : 'N/A' }}</td>
                                        <td>
                                            {% if request.type == 'annual' %}
                                                <span class="badge bg-primary">Annual Leave</span>
                                            {% elseif request.type == 'sick' %}
                                                <span class="badge bg-warning">Sick Leave</span>
                                            {% elseif request.type == 'maternity' %}
                                                <span class="badge bg-info">Maternity</span>
                                            {% elseif request.type == 'paternity' %}
                                                <span class="badge bg-info">Paternity</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Other</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ request.startDate|date('d/m/Y') }} - {{ request.endDate|date('d/m/Y') }}
                                        </td>
                                        <td>{{ request.daysRequested }}</td>
                                        <td>
                                            {% if request.status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elseif request.status == 'manager_approved' %}
                                                <span class="badge bg-info">Manager Approved</span>
                                            {% elseif request.status == 'manager_rejected' %}
                                                <span class="badge bg-danger">Manager Rejected</span>
                                            {% elseif request.status == 'hr_approved' %}
                                                <span class="badge bg-success">HR Approved</span>
                                            {% elseif request.status == 'hr_rejected' %}
                                                <span class="badge bg-danger">HR Rejected</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ request.createdAt|date('d/m/Y H:i') }}</td>
                                        <td>
                                            <a href="{{ path('app_leave_request_show', {id: request.id}) }}" 
                                               class="btn btn-sm btn-info me-1" 
                                               title="View request details">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            {% if request.isManagerApproved %}
                                                <a href="{{ path('app_leave_request_hr_decision', {id: request.id}) }}" 
                                                   class="btn btn-sm btn-success"
                                                   title="Make HR decision on this request">
                                                    <i class="fas fa-gavel"></i> Decide
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
