<?xml version="1.0" encoding="UTF-8"?>
<framework xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="schemas/frameworkDescriptionVersion1.1.4.xsd" frameworkId="org.getcomposer" name="Composer_25/06/2025 17:30_c" invoke="PROJECT_DEFAULT_INTERPRETER C:\xampp\htdocs\composer.phar" alias="c" enabled="true" version="2">
  <command>
    <name>_complete</name>
    <help><![CDATA[Internal command to provide shell completion suggestions<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--shell</td><td>(-s)</td><td>The shell type ("bash")</td></tr> <tr><td>--input</td><td>(-i)</td><td>An array of input tokens (e.g. COMP_WORDS or argv)</td></tr> <tr><td>--current</td><td>(-c)</td><td>The index of the "input" array that the cursor is in (e.g. COMP_CWORD)</td></tr> <tr><td>--symfony</td><td>(-S)</td><td>The version of the completion script</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--shell" shortcut="-s" pattern="equals">
        <help><![CDATA[The shell type ("bash")]]></help>
      </option>
      <option name="--input" shortcut="-i" pattern="equals">
        <help><![CDATA[An array of input tokens (e.g. COMP_WORDS or argv)]]></help>
      </option>
      <option name="--current" shortcut="-c" pattern="equals">
        <help><![CDATA[The index of the "input" array that the cursor is in (e.g. COMP_CWORD)]]></help>
      </option>
      <option name="--symfony" shortcut="-S" pattern="equals">
        <help><![CDATA[The version of the completion script]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>about</name>
    <help><![CDATA[<b>php composer.phar about</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>archive</name>
    <help><![CDATA[The <b>archive</b> command creates an archive of the specified format<br> containing the files and directories of the Composer project or the specified<br> package in the specified version and writes it to the specified directory.<br> <br> <b>php composer.phar archive [--format=zip] [--dir=/foo] [--file=filename] [package [version]]</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#archive<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td>(-f)</td><td>Format of the resulting archive: tar, tar.gz, tar.bz2 or zip (default tar)</td></tr> <tr><td>--dir</td><td></td><td>Write the archive to this directory</td></tr> <tr><td>--file</td><td></td><td>Write the archive with the given file name. Note that the format will be appended.</td></tr> <tr><td>--ignore-filters</td><td></td><td>Ignore filters when saving package</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package[=null] version[=null]</params>
    <optionsBefore>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Format of the resulting archive: tar, tar.gz, tar.bz2 or zip (default tar)]]></help>
      </option>
      <option name="--dir" shortcut="" pattern="equals">
        <help><![CDATA[Write the archive to this directory]]></help>
      </option>
      <option name="--file" shortcut="" pattern="equals">
        <help><![CDATA[Write the archive with the given file name. Note that the format will be appended.]]></help>
      </option>
      <option name="--ignore-filters" shortcut="">
        <help><![CDATA[Ignore filters when saving package]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>audit</name>
    <help><![CDATA[The <b>audit</b> command checks for security vulnerability advisories for installed packages.<br> <br> If you do not want to include dev dependencies in the audit you can omit them with --no-dev<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#audit<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--no-dev</td><td></td><td>Disables auditing of require-dev packages.</td></tr> <tr><td>--format</td><td>(-f)</td><td>Output format. Must be "table", "plain", "json", or "summary".</td></tr> <tr><td>--locked</td><td></td><td>Audit based on the lock file instead of the installed packages.</td></tr> <tr><td>--abandoned</td><td></td><td>Behavior on abandoned packages. Must be "ignore", "report", or "fail".</td></tr> <tr><td>--ignore-severity</td><td></td><td>Ignore advisories of a certain severity level.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables auditing of require-dev packages.]]></help>
      </option>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Output format. Must be "table", "plain", "json", or "summary".]]></help>
      </option>
      <option name="--locked" shortcut="">
        <help><![CDATA[Audit based on the lock file instead of the installed packages.]]></help>
      </option>
      <option name="--abandoned" shortcut="" pattern="equals">
        <help><![CDATA[Behavior on abandoned packages. Must be "ignore", "report", or "fail".]]></help>
      </option>
      <option name="--ignore-severity" shortcut="" pattern="equals">
        <help><![CDATA[Ignore advisories of a certain severity level.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>auto-scripts</name>
    <help><![CDATA[The <b>run-script</b> command runs scripts defined in composer.json:<br> <br> <b>php composer.phar run-script post-update-cmd</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#run-script-run<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--dev</td><td></td><td>Sets the dev mode.</td></tr> <tr><td>--no-dev</td><td></td><td>Disables the dev mode.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>args[=null]</params>
    <optionsBefore>
      <option name="--dev" shortcut="">
        <help><![CDATA[Sets the dev mode.]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables the dev mode.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>browse</name>
    <help><![CDATA[The home command opens or shows a package's repository URL or<br> homepage in your default browser.<br> <br> To open the homepage by default, use -H or --homepage.<br> To show instead of open the repository or homepage URL, use -s or --show.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#browse-home<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--homepage</td><td>(-H)</td><td>Open the homepage instead of the repository URL.</td></tr> <tr><td>--show</td><td>(-s)</td><td>Only show the homepage or repository URL.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--homepage" shortcut="-H">
        <help><![CDATA[Open the homepage instead of the repository URL.]]></help>
      </option>
      <option name="--show" shortcut="-s">
        <help><![CDATA[Only show the homepage or repository URL.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>bump</name>
    <help><![CDATA[The <b>bump</b> command increases the lower limit of your composer.json requirements<br> to the currently installed versions. This helps to ensure your dependencies do not<br> accidentally get downgraded due to some other conflict, and can slightly improve<br> dependency resolution performance as it limits the amount of package versions<br> Composer has to look at.<br> <br> Running this blindly on libraries is **NOT** recommended as it will narrow down<br> your allowed dependencies, which may cause dependency hell for your users.<br> Running it with <b>--dev-only</b> on libraries may be fine however as dev requirements<br> are local to the library and do not affect consumers of the package.<br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--dev-only</td><td>(-D)</td><td>Only bump requirements in "require-dev".</td></tr> <tr><td>--no-dev-only</td><td>(-R)</td><td>Only bump requirements in "require".</td></tr> <tr><td>--dry-run</td><td></td><td>Outputs the packages to bump, but will not execute anything.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--dev-only" shortcut="-D">
        <help><![CDATA[Only bump requirements in "require-dev".]]></help>
      </option>
      <option name="--no-dev-only" shortcut="-R">
        <help><![CDATA[Only bump requirements in "require".]]></help>
      </option>
      <option name="--dry-run" shortcut="">
        <help><![CDATA[Outputs the packages to bump, but will not execute anything.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>check-platform-reqs</name>
    <help><![CDATA[Checks that your PHP and extensions versions match the platform requirements of the installed packages.<br> <br> Unlike update/install, this command will ignore config.platform settings and check the real platform packages so you can be certain you have the required platform dependencies.<br> <br> <b>php composer.phar check-platform-reqs</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--no-dev</td><td></td><td>Disables checking of require-dev packages requirements.</td></tr> <tr><td>--lock</td><td></td><td>Checks requirements only from the lock file, not from installed packages.</td></tr> <tr><td>--format</td><td>(-f)</td><td>Format of the output: text or json</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables checking of require-dev packages requirements.]]></help>
      </option>
      <option name="--lock" shortcut="">
        <help><![CDATA[Checks requirements only from the lock file, not from installed packages.]]></help>
      </option>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Format of the output: text or json]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>clear-cache</name>
    <help><![CDATA[The <b>clear-cache</b> deletes all cached packages from composer's<br> cache directory.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#clear-cache-clearcache-cc<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--gc</td><td></td><td>Only run garbage collection, not a full cache clear</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--gc" shortcut="">
        <help><![CDATA[Only run garbage collection, not a full cache clear]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>completion</name>
    <help><![CDATA[The <b>completion</> command dumps the shell completion script required<br> to use shell autocompletion (currently only bash completion is supported).<br> <br> <comment>Static installation<br> -------------------</><br> <br> Dump the script to a global completion file and restart your shell:<br> <br> <b>C:\xampp\htdocs\composer.phar completion bash | sudo tee /etc/bash_completion.d/composer.phar</><br> <br> Or dump the script to a local file and source it:<br> <br> <b>C:\xampp\htdocs\composer.phar completion bash > completion.sh</><br> <br> <comment># source the file whenever you use the project</><br> <b>source completion.sh</><br> <br> <comment># or add this line at the end of your "~/.bashrc" file:</><br> <b>source /path/to/completion.sh</><br> <br> <comment>Dynamic installation<br> --------------------</><br> <br> Add this to the end of your shell configuration file (e.g. <b>"~/.bashrc"</>):<br> <br> <b>eval "$(C:\xampp\htdocs\composer.phar completion bash)"</><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--debug</td><td></td><td>Tail the completion debug log</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>shell[=null]</params>
    <optionsBefore>
      <option name="--debug" shortcut="">
        <help><![CDATA[Tail the completion debug log]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config</name>
    <help><![CDATA[This command allows you to edit composer config settings and repositories<br> in either the local composer.json file or the global config.json file.<br> <br> Additionally it lets you edit most properties in the local composer.json.<br> <br> To set a config setting:<br> <br> <comment>C:\xampp\htdocs\composer.phar config bin-dir bin/</comment><br> <br> To read a config setting:<br> <br> <comment>C:\xampp\htdocs\composer.phar config bin-dir</comment><br> Outputs: <b>bin</b><br> <br> To edit the global config.json file:<br> <br> <comment>C:\xampp\htdocs\composer.phar config --global</comment><br> <br> To add a repository:<br> <br> <comment>C:\xampp\htdocs\composer.phar config repositories.foo vcs https://bar.com</comment><br> <br> To remove a repository (repo is a short alias for repositories):<br> <br> <comment>C:\xampp\htdocs\composer.phar config --unset repo.foo</comment><br> <br> To disable packagist:<br> <br> <comment>C:\xampp\htdocs\composer.phar config repo.packagist false</comment><br> <br> You can alter repositories in the global config.json file by passing in the<br> <b>--global</b> option.<br> <br> To add or edit suggested packages you can use:<br> <br> <comment>C:\xampp\htdocs\composer.phar config suggest.package reason for the suggestion</comment><br> <br> To add or edit extra properties you can use:<br> <br> <comment>C:\xampp\htdocs\composer.phar config extra.property value</comment><br> <br> Or to add a complex value you can use json with:<br> <br> <comment>C:\xampp\htdocs\composer.phar config extra.property --json '{"foo":true, "bar": []}'</comment><br> <br> To edit the file in an external editor:<br> <br> <comment>C:\xampp\htdocs\composer.phar config --editor</comment><br> <br> To choose your editor you can set the "EDITOR" env variable.<br> <br> To get a list of configuration values in the file:<br> <br> <comment>C:\xampp\htdocs\composer.phar config --list</comment><br> <br> You can always pass more than one option. As an example, if you want to edit the<br> global config.json file.<br> <br> <comment>C:\xampp\htdocs\composer.phar config --editor --global</comment><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#config<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--global</td><td>(-g)</td><td>Apply command to the global config file</td></tr> <tr><td>--editor</td><td>(-e)</td><td>Open editor</td></tr> <tr><td>--auth</td><td>(-a)</td><td>Affect auth config file (only used for --editor)</td></tr> <tr><td>--unset</td><td></td><td>Unset the given setting-key</td></tr> <tr><td>--list</td><td>(-l)</td><td>List configuration settings</td></tr> <tr><td>--file</td><td>(-f)</td><td>If you want to choose a different composer.json or config.json</td></tr> <tr><td>--absolute</td><td></td><td>Returns absolute paths when fetching *-dir config values instead of relative</td></tr> <tr><td>--json</td><td>(-j)</td><td>JSON decode the setting value, to be used with extra.* keys</td></tr> <tr><td>--merge</td><td>(-m)</td><td>Merge the setting value with the current value, to be used with extra.* keys in combination with --json</td></tr> <tr><td>--append</td><td></td><td>When adding a repository, append it (lowest priority) to the existing ones instead of prepending it (highest priority)</td></tr> <tr><td>--source</td><td></td><td>Display where the config value is loaded from</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>setting-key[=null] setting-value[=null]</params>
    <optionsBefore>
      <option name="--global" shortcut="-g">
        <help><![CDATA[Apply command to the global config file]]></help>
      </option>
      <option name="--editor" shortcut="-e">
        <help><![CDATA[Open editor]]></help>
      </option>
      <option name="--auth" shortcut="-a">
        <help><![CDATA[Affect auth config file (only used for --editor)]]></help>
      </option>
      <option name="--unset" shortcut="">
        <help><![CDATA[Unset the given setting-key]]></help>
      </option>
      <option name="--list" shortcut="-l">
        <help><![CDATA[List configuration settings]]></help>
      </option>
      <option name="--file" shortcut="-f" pattern="equals">
        <help><![CDATA[If you want to choose a different composer.json or config.json]]></help>
      </option>
      <option name="--absolute" shortcut="">
        <help><![CDATA[Returns absolute paths when fetching *-dir config values instead of relative]]></help>
      </option>
      <option name="--json" shortcut="-j">
        <help><![CDATA[JSON decode the setting value, to be used with extra.* keys]]></help>
      </option>
      <option name="--merge" shortcut="-m">
        <help><![CDATA[Merge the setting value with the current value, to be used with extra.* keys in combination with --json]]></help>
      </option>
      <option name="--append" shortcut="">
        <help><![CDATA[When adding a repository, append it (lowest priority) to the existing ones instead of prepending it (highest priority)]]></help>
      </option>
      <option name="--source" shortcut="">
        <help><![CDATA[Display where the config value is loaded from]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>create-project</name>
    <help><![CDATA[The <b>create-project</b> command creates a new project from a given<br> package into a new directory. If executed without params and in a directory<br> with a composer.json file it installs the packages for the current project.<br> <br> You can use this command to bootstrap new projects or setup a clean<br> version-controlled installation for developers of your project.<br> <br> <b>php composer.phar create-project vendor/project target-directory [version]</b><br> <br> You can also specify the version with the package name using = or : as separator.<br> <br> <b>php composer.phar create-project vendor/project:version target-directory</b><br> <br> To install unstable packages, either specify the version you want, or use the<br> --stability=dev (where dev can be one of RC, beta, alpha or dev).<br> <br> To setup a developer workable version you should create the project using the source<br> controlled code by appending the <b>'--prefer-source'</b> flag.<br> <br> To install a package from another repository than the default one you<br> can pass the <b>'--repository=https://myrepository.org'</b> flag.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#create-project<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--stability</td><td>(-s)</td><td>Minimum-stability allowed (unless a version is specified).</td></tr> <tr><td>--prefer-source</td><td></td><td>Forces installation from package sources when possible, including VCS information.</td></tr> <tr><td>--prefer-dist</td><td></td><td>Forces installation from package dist (default behavior).</td></tr> <tr><td>--prefer-install</td><td></td><td>Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).</td></tr> <tr><td>--repository</td><td></td><td>Add custom repositories to look the package up, either by URL or using JSON arrays</td></tr> <tr><td>--repository-url</td><td></td><td>DEPRECATED: Use --repository instead.</td></tr> <tr><td>--add-repository</td><td></td><td>Add the custom repository in the composer.json. If a lock file is present it will be deleted and an update will be run instead of install.</td></tr> <tr><td>--dev</td><td></td><td>Enables installation of require-dev packages (enabled by default, only present for BC).</td></tr> <tr><td>--no-dev</td><td></td><td>Disables installation of require-dev packages.</td></tr> <tr><td>--no-custom-installers</td><td></td><td>DEPRECATED: Use no-plugins instead.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--no-progress</td><td></td><td>Do not output download progress.</td></tr> <tr><td>--no-secure-http</td><td></td><td>Disable the secure-http config option temporarily while installing the root package. Use at your own risk. Using this flag is a bad idea.</td></tr> <tr><td>--keep-vcs</td><td></td><td>Whether to prevent deleting the vcs folder.</td></tr> <tr><td>--remove-vcs</td><td></td><td>Whether to force deletion of the vcs folder without prompting.</td></tr> <tr><td>--no-install</td><td></td><td>Whether to skip installation of the package dependencies.</td></tr> <tr><td>--no-audit</td><td></td><td>Whether to skip auditing of the installed package dependencies (can also be set via the COMPOSER_NO_AUDIT=1 env var).</td></tr> <tr><td>--audit-format</td><td></td><td>Audit output format. Must be "table", "plain", "json" or "summary".</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages).</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages).</td></tr> <tr><td>--ask</td><td></td><td>Whether to ask for project directory.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package[=null] directory[=null] version[=null]</params>
    <optionsBefore>
      <option name="--stability" shortcut="-s" pattern="equals">
        <help><![CDATA[Minimum-stability allowed (unless a version is specified).]]></help>
      </option>
      <option name="--prefer-source" shortcut="">
        <help><![CDATA[Forces installation from package sources when possible, including VCS information.]]></help>
      </option>
      <option name="--prefer-dist" shortcut="">
        <help><![CDATA[Forces installation from package dist (default behavior).]]></help>
      </option>
      <option name="--prefer-install" shortcut="" pattern="equals">
        <help><![CDATA[Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).]]></help>
      </option>
      <option name="--repository" shortcut="" pattern="equals">
        <help><![CDATA[Add custom repositories to look the package up, either by URL or using JSON arrays]]></help>
      </option>
      <option name="--repository-url" shortcut="" pattern="equals">
        <help><![CDATA[DEPRECATED: Use --repository instead.]]></help>
      </option>
      <option name="--add-repository" shortcut="">
        <help><![CDATA[Add the custom repository in the composer.json. If a lock file is present it will be deleted and an update will be run instead of install.]]></help>
      </option>
      <option name="--dev" shortcut="">
        <help><![CDATA[Enables installation of require-dev packages (enabled by default, only present for BC).]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables installation of require-dev packages.]]></help>
      </option>
      <option name="--no-custom-installers" shortcut="">
        <help><![CDATA[DEPRECATED: Use no-plugins instead.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--no-progress" shortcut="">
        <help><![CDATA[Do not output download progress.]]></help>
      </option>
      <option name="--no-secure-http" shortcut="">
        <help><![CDATA[Disable the secure-http config option temporarily while installing the root package. Use at your own risk. Using this flag is a bad idea.]]></help>
      </option>
      <option name="--keep-vcs" shortcut="">
        <help><![CDATA[Whether to prevent deleting the vcs folder.]]></help>
      </option>
      <option name="--remove-vcs" shortcut="">
        <help><![CDATA[Whether to force deletion of the vcs folder without prompting.]]></help>
      </option>
      <option name="--no-install" shortcut="">
        <help><![CDATA[Whether to skip installation of the package dependencies.]]></help>
      </option>
      <option name="--no-audit" shortcut="">
        <help><![CDATA[Whether to skip auditing of the installed package dependencies (can also be set via the COMPOSER_NO_AUDIT=1 env var).]]></help>
      </option>
      <option name="--audit-format" shortcut="" pattern="equals">
        <help><![CDATA[Audit output format. Must be "table", "plain", "json" or "summary".]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages).]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages).]]></help>
      </option>
      <option name="--ask" shortcut="">
        <help><![CDATA[Whether to ask for project directory.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>depends</name>
    <help><![CDATA[Displays detailed information about where a package is referenced.<br> <br> <b>php composer.phar depends composer/composer</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#depends-why<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--recursive</td><td>(-r)</td><td>Recursively resolves up to the root package</td></tr> <tr><td>--tree</td><td>(-t)</td><td>Prints the results as a nested tree</td></tr> <tr><td>--locked</td><td></td><td>Read dependency information from composer.lock</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package</params>
    <optionsBefore>
      <option name="--recursive" shortcut="-r">
        <help><![CDATA[Recursively resolves up to the root package]]></help>
      </option>
      <option name="--tree" shortcut="-t">
        <help><![CDATA[Prints the results as a nested tree]]></help>
      </option>
      <option name="--locked" shortcut="">
        <help><![CDATA[Read dependency information from composer.lock]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>diagnose</name>
    <help><![CDATA[The <b>diagnose</b> command checks common errors to help debugging problems.<br> <br> The process exit code will be 1 in case of warnings and 2 for errors.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#diagnose<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>dump-autoload</name>
    <help><![CDATA[<b>php composer.phar dump-autoload</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#dump-autoload-dumpautoload<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--optimize</td><td>(-o)</td><td>Optimizes PSR0 and PSR4 packages to be loaded with classmaps too, good for production.</td></tr> <tr><td>--classmap-authoritative</td><td>(-a)</td><td>Autoload classes from the classmap only. Implicitly enables `--optimize`.</td></tr> <tr><td>--apcu</td><td></td><td>Use APCu to cache found/not-found classes.</td></tr> <tr><td>--apcu-prefix</td><td></td><td>Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu</td></tr> <tr><td>--dry-run</td><td></td><td>Outputs the operations but will not execute anything.</td></tr> <tr><td>--dev</td><td></td><td>Enables autoload-dev rules. Composer will by default infer this automatically according to the last install or update --no-dev state.</td></tr> <tr><td>--no-dev</td><td></td><td>Disables autoload-dev rules. Composer will by default infer this automatically according to the last install or update --no-dev state.</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages).</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages).</td></tr> <tr><td>--strict-psr</td><td></td><td>Return a failed status code (1) if PSR-4 or PSR-0 mapping errors are present. Requires --optimize to work.</td></tr> <tr><td>--strict-ambiguous</td><td></td><td>Return a failed status code (2) if the same class is found in multiple files. Requires --optimize to work.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--optimize" shortcut="-o">
        <help><![CDATA[Optimizes PSR0 and PSR4 packages to be loaded with classmaps too, good for production.]]></help>
      </option>
      <option name="--classmap-authoritative" shortcut="-a">
        <help><![CDATA[Autoload classes from the classmap only. Implicitly enables `--optimize`.]]></help>
      </option>
      <option name="--apcu" shortcut="">
        <help><![CDATA[Use APCu to cache found/not-found classes.]]></help>
      </option>
      <option name="--apcu-prefix" shortcut="" pattern="equals">
        <help><![CDATA[Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu]]></help>
      </option>
      <option name="--dry-run" shortcut="">
        <help><![CDATA[Outputs the operations but will not execute anything.]]></help>
      </option>
      <option name="--dev" shortcut="">
        <help><![CDATA[Enables autoload-dev rules. Composer will by default infer this automatically according to the last install or update --no-dev state.]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables autoload-dev rules. Composer will by default infer this automatically according to the last install or update --no-dev state.]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages).]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages).]]></help>
      </option>
      <option name="--strict-psr" shortcut="">
        <help><![CDATA[Return a failed status code (1) if PSR-4 or PSR-0 mapping errors are present. Requires --optimize to work.]]></help>
      </option>
      <option name="--strict-ambiguous" shortcut="">
        <help><![CDATA[Return a failed status code (2) if the same class is found in multiple files. Requires --optimize to work.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>exec</name>
    <help><![CDATA[Executes a vendored binary/script.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#exec<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--list</td><td>(-l)</td><td></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>binary[=null] args[=null]</params>
    <optionsBefore>
      <option name="--list" shortcut="-l" />
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>fund</name>
    <help><![CDATA[Discover how to help fund the maintenance of your dependencies<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td>(-f)</td><td>Format of the output: text or json</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Format of the output: text or json]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>global</name>
    <help><![CDATA[Use this command as a wrapper to run other Composer commands<br> within the global context of COMPOSER_HOME.<br> <br> You can use this to install CLI utilities globally, all you need<br> is to add the COMPOSER_HOME/vendor/bin dir to your PATH env var.<br> <br> COMPOSER_HOME is c:\Users\<USER>\AppData\Roaming\Composer on Windows<br> and /home/<USER>/.composer on unix systems.<br> <br> If your system uses freedesktop.org standards, then it will first check<br> XDG_CONFIG_HOME or default to /home/<USER>/.config/composer<br> <br> Note: This path may vary depending on customizations to bin-dir in<br> composer.json or the environmental variable COMPOSER_BIN_DIR.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#global<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>command-name args[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>help</name>
    <help><![CDATA[The <b>help</b> command displays help for a given command:<br> <br> <b>C:\xampp\htdocs\composer.phar help list</b><br> <br> You can also output the help in other formats by using the <comment>--format</comment> option:<br> <br> <b>C:\xampp\htdocs\composer.phar help --format=xml list</b><br> <br> To display the list of available commands, please use the <b>list</b> command.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--raw</td><td></td><td>To output raw command help</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>command_name[=null]</params>
    <optionsBefore>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command help]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>init</name>
    <help><![CDATA[The <b>init</b> command creates a basic composer.json file<br> in the current directory.<br> <br> <b>php composer.phar init</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#init<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--name</td><td></td><td>Name of the package</td></tr> <tr><td>--description</td><td></td><td>Description of package</td></tr> <tr><td>--author</td><td></td><td>Author name of package</td></tr> <tr><td>--type</td><td></td><td>Type of package (e.g. library, project, metapackage, composer-plugin)</td></tr> <tr><td>--homepage</td><td></td><td>Homepage of package</td></tr> <tr><td>--require</td><td></td><td>Package to require with a version constraint, e.g. foo/bar:1.0.0 or foo/bar=1.0.0 or "foo/bar 1.0.0"</td></tr> <tr><td>--require-dev</td><td></td><td>Package to require for development with a version constraint, e.g. foo/bar:1.0.0 or foo/bar=1.0.0 or "foo/bar 1.0.0"</td></tr> <tr><td>--stability</td><td>(-s)</td><td>Minimum stability (empty or one of: stable, RC, beta, alpha, dev)</td></tr> <tr><td>--license</td><td>(-l)</td><td>License of package</td></tr> <tr><td>--repository</td><td></td><td>Add custom repositories, either by URL or using JSON arrays</td></tr> <tr><td>--autoload</td><td>(-a)</td><td>Add PSR-4 autoload mapping. Maps your package's namespace to the provided directory. (Expects a relative path, e.g. src/)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[Name of the package]]></help>
      </option>
      <option name="--description" shortcut="" pattern="equals">
        <help><![CDATA[Description of package]]></help>
      </option>
      <option name="--author" shortcut="" pattern="equals">
        <help><![CDATA[Author name of package]]></help>
      </option>
      <option name="--type" shortcut="" pattern="equals">
        <help><![CDATA[Type of package (e.g. library, project, metapackage, composer-plugin)]]></help>
      </option>
      <option name="--homepage" shortcut="" pattern="equals">
        <help><![CDATA[Homepage of package]]></help>
      </option>
      <option name="--require" shortcut="" pattern="equals">
        <help><![CDATA[Package to require with a version constraint, e.g. foo/bar:1.0.0 or foo/bar=1.0.0 or "foo/bar 1.0.0"]]></help>
      </option>
      <option name="--require-dev" shortcut="" pattern="equals">
        <help><![CDATA[Package to require for development with a version constraint, e.g. foo/bar:1.0.0 or foo/bar=1.0.0 or "foo/bar 1.0.0"]]></help>
      </option>
      <option name="--stability" shortcut="-s" pattern="equals">
        <help><![CDATA[Minimum stability (empty or one of: stable, RC, beta, alpha, dev)]]></help>
      </option>
      <option name="--license" shortcut="-l" pattern="equals">
        <help><![CDATA[License of package]]></help>
      </option>
      <option name="--repository" shortcut="" pattern="equals">
        <help><![CDATA[Add custom repositories, either by URL or using JSON arrays]]></help>
      </option>
      <option name="--autoload" shortcut="-a" pattern="equals">
        <help><![CDATA[Add PSR-4 autoload mapping. Maps your package's namespace to the provided directory. (Expects a relative path, e.g. src/)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>install</name>
    <help><![CDATA[The <b>install</b> command reads the composer.lock file from<br> the current directory, processes it, and downloads and installs all the<br> libraries and dependencies outlined in that file. If the file does not<br> exist it will look for composer.json and do the same.<br> <br> <b>php composer.phar install</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#install-i<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--prefer-source</td><td></td><td>Forces installation from package sources when possible, including VCS information.</td></tr> <tr><td>--prefer-dist</td><td></td><td>Forces installation from package dist (default behavior).</td></tr> <tr><td>--prefer-install</td><td></td><td>Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).</td></tr> <tr><td>--dry-run</td><td></td><td>Outputs the operations but will not execute anything (implicitly enables --verbose).</td></tr> <tr><td>--download-only</td><td></td><td>Download only, do not install packages.</td></tr> <tr><td>--dev</td><td></td><td>DEPRECATED: Enables installation of require-dev packages (enabled by default, only present for BC).</td></tr> <tr><td>--no-suggest</td><td></td><td>DEPRECATED: This flag does not exist anymore.</td></tr> <tr><td>--no-dev</td><td></td><td>Disables installation of require-dev packages.</td></tr> <tr><td>--no-autoloader</td><td></td><td>Skips autoloader generation</td></tr> <tr><td>--no-progress</td><td></td><td>Do not output download progress.</td></tr> <tr><td>--no-install</td><td></td><td>Do not use, only defined here to catch misuse of the install command.</td></tr> <tr><td>--audit</td><td></td><td>Run an audit after installation is complete.</td></tr> <tr><td>--audit-format</td><td></td><td>Audit output format. Must be "table", "plain", "json", or "summary".</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--optimize-autoloader</td><td>(-o)</td><td>Optimize autoloader during autoloader dump</td></tr> <tr><td>--classmap-authoritative</td><td>(-a)</td><td>Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.</td></tr> <tr><td>--apcu-autoloader</td><td></td><td>Use APCu to cache found/not-found classes.</td></tr> <tr><td>--apcu-autoloader-prefix</td><td></td><td>Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages).</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages).</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--prefer-source" shortcut="">
        <help><![CDATA[Forces installation from package sources when possible, including VCS information.]]></help>
      </option>
      <option name="--prefer-dist" shortcut="">
        <help><![CDATA[Forces installation from package dist (default behavior).]]></help>
      </option>
      <option name="--prefer-install" shortcut="" pattern="equals">
        <help><![CDATA[Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).]]></help>
      </option>
      <option name="--dry-run" shortcut="">
        <help><![CDATA[Outputs the operations but will not execute anything (implicitly enables --verbose).]]></help>
      </option>
      <option name="--download-only" shortcut="">
        <help><![CDATA[Download only, do not install packages.]]></help>
      </option>
      <option name="--dev" shortcut="">
        <help><![CDATA[DEPRECATED: Enables installation of require-dev packages (enabled by default, only present for BC).]]></help>
      </option>
      <option name="--no-suggest" shortcut="">
        <help><![CDATA[DEPRECATED: This flag does not exist anymore.]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables installation of require-dev packages.]]></help>
      </option>
      <option name="--no-autoloader" shortcut="">
        <help><![CDATA[Skips autoloader generation]]></help>
      </option>
      <option name="--no-progress" shortcut="">
        <help><![CDATA[Do not output download progress.]]></help>
      </option>
      <option name="--no-install" shortcut="">
        <help><![CDATA[Do not use, only defined here to catch misuse of the install command.]]></help>
      </option>
      <option name="--audit" shortcut="">
        <help><![CDATA[Run an audit after installation is complete.]]></help>
      </option>
      <option name="--audit-format" shortcut="" pattern="equals">
        <help><![CDATA[Audit output format. Must be "table", "plain", "json", or "summary".]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--optimize-autoloader" shortcut="-o">
        <help><![CDATA[Optimize autoloader during autoloader dump]]></help>
      </option>
      <option name="--classmap-authoritative" shortcut="-a">
        <help><![CDATA[Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.]]></help>
      </option>
      <option name="--apcu-autoloader" shortcut="">
        <help><![CDATA[Use APCu to cache found/not-found classes.]]></help>
      </option>
      <option name="--apcu-autoloader-prefix" shortcut="" pattern="equals">
        <help><![CDATA[Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages).]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages).]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>licenses</name>
    <help><![CDATA[The license command displays detailed information about the licenses of<br> the installed dependencies.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#licenses<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td>(-f)</td><td>Format of the output: text, json or summary</td></tr> <tr><td>--no-dev</td><td></td><td>Disables search in require-dev packages.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Format of the output: text, json or summary]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables search in require-dev packages.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>list</name>
    <help><![CDATA[The <b>list</b> command lists all commands:<br> <br> <b>C:\xampp\htdocs\composer.phar list</b><br> <br> You can also display the commands for a specific namespace:<br> <br> <b>C:\xampp\htdocs\composer.phar list test</b><br> <br> You can also output the information in other formats by using the <comment>--format</comment> option:<br> <br> <b>C:\xampp\htdocs\composer.phar list --format=xml</b><br> <br> It's also possible to get raw list of commands (useful for embedding command runner):<br> <br> <b>C:\xampp\htdocs\composer.phar list --raw</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--raw</td><td></td><td>To output raw command list</td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--short</td><td></td><td>To skip describing commands' arguments</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>namespace[=null]</params>
    <optionsBefore>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command list]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--short" shortcut="">
        <help><![CDATA[To skip describing commands' arguments]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>outdated</name>
    <help><![CDATA[The outdated command is just a proxy for `composer show -l`<br> <br> The color coding (or signage if you have ANSI colors disabled) for dependency versions is as such:<br> <br> - <b>green</b> (=): Dependency is in the latest version and is up to date.<br> - <comment>yellow</comment> (~): Dependency has a new version available that includes backwards<br> compatibility breaks according to semver, so upgrade when you can but it<br> may involve work.<br> - <highlight>red</highlight> (!): Dependency has a new version that is semver-compatible and you should upgrade it.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#outdated<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--outdated</td><td>(-o)</td><td>Show only packages that are outdated (this is the default, but present here for compat with `show`</td></tr> <tr><td>--all</td><td>(-a)</td><td>Show all installed packages with their latest versions</td></tr> <tr><td>--locked</td><td></td><td>Shows updates for packages from the lock file, regardless of what is currently in vendor dir</td></tr> <tr><td>--direct</td><td>(-D)</td><td>Shows only packages that are directly required by the root package</td></tr> <tr><td>--strict</td><td></td><td>Return a non-zero exit code when there are outdated packages</td></tr> <tr><td>--major-only</td><td>(-M)</td><td>Show only packages that have major SemVer-compatible updates.</td></tr> <tr><td>--minor-only</td><td>(-m)</td><td>Show only packages that have minor SemVer-compatible updates.</td></tr> <tr><td>--patch-only</td><td>(-p)</td><td>Show only packages that have patch SemVer-compatible updates.</td></tr> <tr><td>--sort-by-age</td><td>(-A)</td><td>Displays the installed version's age, and sorts packages oldest first.</td></tr> <tr><td>--format</td><td>(-f)</td><td>Format of the output: text or json</td></tr> <tr><td>--ignore</td><td></td><td>Ignore specified package(s). Can contain wildcards (*). Use it if you don't want to be informed about new versions of some packages.</td></tr> <tr><td>--no-dev</td><td></td><td>Disables search in require-dev packages.</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages). Use with the --outdated option</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages). Use with the --outdated option</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package[=null]</params>
    <optionsBefore>
      <option name="--outdated" shortcut="-o">
        <help><![CDATA[Show only packages that are outdated (this is the default, but present here for compat with `show`]]></help>
      </option>
      <option name="--all" shortcut="-a">
        <help><![CDATA[Show all installed packages with their latest versions]]></help>
      </option>
      <option name="--locked" shortcut="">
        <help><![CDATA[Shows updates for packages from the lock file, regardless of what is currently in vendor dir]]></help>
      </option>
      <option name="--direct" shortcut="-D">
        <help><![CDATA[Shows only packages that are directly required by the root package]]></help>
      </option>
      <option name="--strict" shortcut="">
        <help><![CDATA[Return a non-zero exit code when there are outdated packages]]></help>
      </option>
      <option name="--major-only" shortcut="-M">
        <help><![CDATA[Show only packages that have major SemVer-compatible updates.]]></help>
      </option>
      <option name="--minor-only" shortcut="-m">
        <help><![CDATA[Show only packages that have minor SemVer-compatible updates.]]></help>
      </option>
      <option name="--patch-only" shortcut="-p">
        <help><![CDATA[Show only packages that have patch SemVer-compatible updates.]]></help>
      </option>
      <option name="--sort-by-age" shortcut="-A">
        <help><![CDATA[Displays the installed version's age, and sorts packages oldest first.]]></help>
      </option>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Format of the output: text or json]]></help>
      </option>
      <option name="--ignore" shortcut="" pattern="equals">
        <help><![CDATA[Ignore specified package(s). Can contain wildcards (*). Use it if you don't want to be informed about new versions of some packages.]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables search in require-dev packages.]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages). Use with the --outdated option]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages). Use with the --outdated option]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>prohibits</name>
    <help><![CDATA[Displays detailed information about why a package cannot be installed.<br> <br> <b>php composer.phar prohibits composer/composer</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#prohibits-why-not<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--recursive</td><td>(-r)</td><td>Recursively resolves up to the root package</td></tr> <tr><td>--tree</td><td>(-t)</td><td>Prints the results as a nested tree</td></tr> <tr><td>--locked</td><td></td><td>Read dependency information from composer.lock</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package version</params>
    <optionsBefore>
      <option name="--recursive" shortcut="-r">
        <help><![CDATA[Recursively resolves up to the root package]]></help>
      </option>
      <option name="--tree" shortcut="-t">
        <help><![CDATA[Prints the results as a nested tree]]></help>
      </option>
      <option name="--locked" shortcut="">
        <help><![CDATA[Read dependency information from composer.lock]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>reinstall</name>
    <help><![CDATA[The <b>reinstall</b> command looks up installed packages by name,<br> uninstalls them and reinstalls them. This lets you do a clean install<br> of a package if you messed with its files, or if you wish to change<br> the installation type using --prefer-install.<br> <br> <b>php composer.phar reinstall acme/foo "acme/bar-*"</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#reinstall<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--prefer-source</td><td></td><td>Forces installation from package sources when possible, including VCS information.</td></tr> <tr><td>--prefer-dist</td><td></td><td>Forces installation from package dist (default behavior).</td></tr> <tr><td>--prefer-install</td><td></td><td>Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).</td></tr> <tr><td>--no-autoloader</td><td></td><td>Skips autoloader generation</td></tr> <tr><td>--no-progress</td><td></td><td>Do not output download progress.</td></tr> <tr><td>--optimize-autoloader</td><td>(-o)</td><td>Optimize autoloader during autoloader dump</td></tr> <tr><td>--classmap-authoritative</td><td>(-a)</td><td>Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.</td></tr> <tr><td>--apcu-autoloader</td><td></td><td>Use APCu to cache found/not-found classes.</td></tr> <tr><td>--apcu-autoloader-prefix</td><td></td><td>Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages).</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages).</td></tr> <tr><td>--type</td><td></td><td>Filter packages to reinstall by type(s)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--prefer-source" shortcut="">
        <help><![CDATA[Forces installation from package sources when possible, including VCS information.]]></help>
      </option>
      <option name="--prefer-dist" shortcut="">
        <help><![CDATA[Forces installation from package dist (default behavior).]]></help>
      </option>
      <option name="--prefer-install" shortcut="" pattern="equals">
        <help><![CDATA[Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).]]></help>
      </option>
      <option name="--no-autoloader" shortcut="">
        <help><![CDATA[Skips autoloader generation]]></help>
      </option>
      <option name="--no-progress" shortcut="">
        <help><![CDATA[Do not output download progress.]]></help>
      </option>
      <option name="--optimize-autoloader" shortcut="-o">
        <help><![CDATA[Optimize autoloader during autoloader dump]]></help>
      </option>
      <option name="--classmap-authoritative" shortcut="-a">
        <help><![CDATA[Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.]]></help>
      </option>
      <option name="--apcu-autoloader" shortcut="">
        <help><![CDATA[Use APCu to cache found/not-found classes.]]></help>
      </option>
      <option name="--apcu-autoloader-prefix" shortcut="" pattern="equals">
        <help><![CDATA[Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages).]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages).]]></help>
      </option>
      <option name="--type" shortcut="" pattern="equals">
        <help><![CDATA[Filter packages to reinstall by type(s)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>remove</name>
    <help><![CDATA[The <b>remove</b> command removes a package from the current<br> list of installed packages<br> <br> <b>php composer.phar remove</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#remove-rm<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--dev</td><td></td><td>Removes a package from the require-dev section.</td></tr> <tr><td>--dry-run</td><td></td><td>Outputs the operations but will not execute anything (implicitly enables --verbose).</td></tr> <tr><td>--no-progress</td><td></td><td>Do not output download progress.</td></tr> <tr><td>--no-update</td><td></td><td>Disables the automatic update of the dependencies (implies --no-install).</td></tr> <tr><td>--no-install</td><td></td><td>Skip the install step after updating the composer.lock file.</td></tr> <tr><td>--no-audit</td><td></td><td>Skip the audit step after updating the composer.lock file (can also be set via the COMPOSER_NO_AUDIT=1 env var).</td></tr> <tr><td>--audit-format</td><td></td><td>Audit output format. Must be "table", "plain", "json", or "summary".</td></tr> <tr><td>--update-no-dev</td><td></td><td>Run the dependency update with the --no-dev option.</td></tr> <tr><td>--update-with-dependencies</td><td>(-w)</td><td>Allows inherited dependencies to be updated with explicit dependencies (can also be set via the COMPOSER_WITH_DEPENDENCIES=1 env var). (Deprecated, is now default behavior)</td></tr> <tr><td>--update-with-all-dependencies</td><td>(-W)</td><td>Allows all inherited dependencies to be updated, including those that are root requirements (can also be set via the COMPOSER_WITH_ALL_DEPENDENCIES=1 env var).</td></tr> <tr><td>--with-all-dependencies</td><td></td><td>Alias for --update-with-all-dependencies</td></tr> <tr><td>--no-update-with-dependencies</td><td></td><td>Does not allow inherited dependencies to be updated with explicit dependencies.</td></tr> <tr><td>--minimal-changes</td><td>(-m)</td><td>During an update with -w/-W, only perform absolutely necessary changes to transitive dependencies (can also be set via the COMPOSER_MINIMAL_CHANGES=1 env var).</td></tr> <tr><td>--unused</td><td></td><td>Remove all packages which are locked but not required by any other package.</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages).</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages).</td></tr> <tr><td>--optimize-autoloader</td><td>(-o)</td><td>Optimize autoloader during autoloader dump</td></tr> <tr><td>--classmap-authoritative</td><td>(-a)</td><td>Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.</td></tr> <tr><td>--apcu-autoloader</td><td></td><td>Use APCu to cache found/not-found classes.</td></tr> <tr><td>--apcu-autoloader-prefix</td><td></td><td>Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--dev" shortcut="">
        <help><![CDATA[Removes a package from the require-dev section.]]></help>
      </option>
      <option name="--dry-run" shortcut="">
        <help><![CDATA[Outputs the operations but will not execute anything (implicitly enables --verbose).]]></help>
      </option>
      <option name="--no-progress" shortcut="">
        <help><![CDATA[Do not output download progress.]]></help>
      </option>
      <option name="--no-update" shortcut="">
        <help><![CDATA[Disables the automatic update of the dependencies (implies --no-install).]]></help>
      </option>
      <option name="--no-install" shortcut="">
        <help><![CDATA[Skip the install step after updating the composer.lock file.]]></help>
      </option>
      <option name="--no-audit" shortcut="">
        <help><![CDATA[Skip the audit step after updating the composer.lock file (can also be set via the COMPOSER_NO_AUDIT=1 env var).]]></help>
      </option>
      <option name="--audit-format" shortcut="" pattern="equals">
        <help><![CDATA[Audit output format. Must be "table", "plain", "json", or "summary".]]></help>
      </option>
      <option name="--update-no-dev" shortcut="">
        <help><![CDATA[Run the dependency update with the --no-dev option.]]></help>
      </option>
      <option name="--update-with-dependencies" shortcut="-w">
        <help><![CDATA[Allows inherited dependencies to be updated with explicit dependencies (can also be set via the COMPOSER_WITH_DEPENDENCIES=1 env var). (Deprecated, is now default behavior)]]></help>
      </option>
      <option name="--update-with-all-dependencies" shortcut="-W">
        <help><![CDATA[Allows all inherited dependencies to be updated, including those that are root requirements (can also be set via the COMPOSER_WITH_ALL_DEPENDENCIES=1 env var).]]></help>
      </option>
      <option name="--with-all-dependencies" shortcut="">
        <help><![CDATA[Alias for --update-with-all-dependencies]]></help>
      </option>
      <option name="--no-update-with-dependencies" shortcut="">
        <help><![CDATA[Does not allow inherited dependencies to be updated with explicit dependencies.]]></help>
      </option>
      <option name="--minimal-changes" shortcut="-m">
        <help><![CDATA[During an update with -w/-W, only perform absolutely necessary changes to transitive dependencies (can also be set via the COMPOSER_MINIMAL_CHANGES=1 env var).]]></help>
      </option>
      <option name="--unused" shortcut="">
        <help><![CDATA[Remove all packages which are locked but not required by any other package.]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages).]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages).]]></help>
      </option>
      <option name="--optimize-autoloader" shortcut="-o">
        <help><![CDATA[Optimize autoloader during autoloader dump]]></help>
      </option>
      <option name="--classmap-authoritative" shortcut="-a">
        <help><![CDATA[Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.]]></help>
      </option>
      <option name="--apcu-autoloader" shortcut="">
        <help><![CDATA[Use APCu to cache found/not-found classes.]]></help>
      </option>
      <option name="--apcu-autoloader-prefix" shortcut="" pattern="equals">
        <help><![CDATA[Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>require</name>
    <help><![CDATA[The require command adds required packages to your composer.json and installs them.<br> <br> If you do not specify a package, composer will prompt you to search for a package, and given results, provide a list of<br> matches to require.<br> <br> If you do not specify a version constraint, composer will choose a suitable one based on the available package versions.<br> <br> If you do not want to install the new dependencies immediately you can call it with --no-update<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#require-r<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--dev</td><td></td><td>Add requirement to require-dev.</td></tr> <tr><td>--dry-run</td><td></td><td>Outputs the operations but will not execute anything (implicitly enables --verbose).</td></tr> <tr><td>--prefer-source</td><td></td><td>Forces installation from package sources when possible, including VCS information.</td></tr> <tr><td>--prefer-dist</td><td></td><td>Forces installation from package dist (default behavior).</td></tr> <tr><td>--prefer-install</td><td></td><td>Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).</td></tr> <tr><td>--fixed</td><td></td><td>Write fixed version to the composer.json.</td></tr> <tr><td>--no-suggest</td><td></td><td>DEPRECATED: This flag does not exist anymore.</td></tr> <tr><td>--no-progress</td><td></td><td>Do not output download progress.</td></tr> <tr><td>--no-update</td><td></td><td>Disables the automatic update of the dependencies (implies --no-install).</td></tr> <tr><td>--no-install</td><td></td><td>Skip the install step after updating the composer.lock file.</td></tr> <tr><td>--no-audit</td><td></td><td>Skip the audit step after updating the composer.lock file (can also be set via the COMPOSER_NO_AUDIT=1 env var).</td></tr> <tr><td>--audit-format</td><td></td><td>Audit output format. Must be "table", "plain", "json", or "summary".</td></tr> <tr><td>--update-no-dev</td><td></td><td>Run the dependency update with the --no-dev option.</td></tr> <tr><td>--update-with-dependencies</td><td>(-w)</td><td>Allows inherited dependencies to be updated, except those that are root requirements (can also be set via the COMPOSER_WITH_DEPENDENCIES=1 env var).</td></tr> <tr><td>--update-with-all-dependencies</td><td>(-W)</td><td>Allows all inherited dependencies to be updated, including those that are root requirements (can also be set via the COMPOSER_WITH_ALL_DEPENDENCIES=1 env var).</td></tr> <tr><td>--with-dependencies</td><td></td><td>Alias for --update-with-dependencies</td></tr> <tr><td>--with-all-dependencies</td><td></td><td>Alias for --update-with-all-dependencies</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages).</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages).</td></tr> <tr><td>--prefer-stable</td><td></td><td>Prefer stable versions of dependencies (can also be set via the COMPOSER_PREFER_STABLE=1 env var).</td></tr> <tr><td>--prefer-lowest</td><td></td><td>Prefer lowest versions of dependencies (can also be set via the COMPOSER_PREFER_LOWEST=1 env var).</td></tr> <tr><td>--minimal-changes</td><td>(-m)</td><td>During an update with -w/-W, only perform absolutely necessary changes to transitive dependencies (can also be set via the COMPOSER_MINIMAL_CHANGES=1 env var).</td></tr> <tr><td>--sort-packages</td><td></td><td>Sorts packages when adding/updating a new dependency</td></tr> <tr><td>--optimize-autoloader</td><td>(-o)</td><td>Optimize autoloader during autoloader dump</td></tr> <tr><td>--classmap-authoritative</td><td>(-a)</td><td>Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.</td></tr> <tr><td>--apcu-autoloader</td><td></td><td>Use APCu to cache found/not-found classes.</td></tr> <tr><td>--apcu-autoloader-prefix</td><td></td><td>Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--dev" shortcut="">
        <help><![CDATA[Add requirement to require-dev.]]></help>
      </option>
      <option name="--dry-run" shortcut="">
        <help><![CDATA[Outputs the operations but will not execute anything (implicitly enables --verbose).]]></help>
      </option>
      <option name="--prefer-source" shortcut="">
        <help><![CDATA[Forces installation from package sources when possible, including VCS information.]]></help>
      </option>
      <option name="--prefer-dist" shortcut="">
        <help><![CDATA[Forces installation from package dist (default behavior).]]></help>
      </option>
      <option name="--prefer-install" shortcut="" pattern="equals">
        <help><![CDATA[Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).]]></help>
      </option>
      <option name="--fixed" shortcut="">
        <help><![CDATA[Write fixed version to the composer.json.]]></help>
      </option>
      <option name="--no-suggest" shortcut="">
        <help><![CDATA[DEPRECATED: This flag does not exist anymore.]]></help>
      </option>
      <option name="--no-progress" shortcut="">
        <help><![CDATA[Do not output download progress.]]></help>
      </option>
      <option name="--no-update" shortcut="">
        <help><![CDATA[Disables the automatic update of the dependencies (implies --no-install).]]></help>
      </option>
      <option name="--no-install" shortcut="">
        <help><![CDATA[Skip the install step after updating the composer.lock file.]]></help>
      </option>
      <option name="--no-audit" shortcut="">
        <help><![CDATA[Skip the audit step after updating the composer.lock file (can also be set via the COMPOSER_NO_AUDIT=1 env var).]]></help>
      </option>
      <option name="--audit-format" shortcut="" pattern="equals">
        <help><![CDATA[Audit output format. Must be "table", "plain", "json", or "summary".]]></help>
      </option>
      <option name="--update-no-dev" shortcut="">
        <help><![CDATA[Run the dependency update with the --no-dev option.]]></help>
      </option>
      <option name="--update-with-dependencies" shortcut="-w">
        <help><![CDATA[Allows inherited dependencies to be updated, except those that are root requirements (can also be set via the COMPOSER_WITH_DEPENDENCIES=1 env var).]]></help>
      </option>
      <option name="--update-with-all-dependencies" shortcut="-W">
        <help><![CDATA[Allows all inherited dependencies to be updated, including those that are root requirements (can also be set via the COMPOSER_WITH_ALL_DEPENDENCIES=1 env var).]]></help>
      </option>
      <option name="--with-dependencies" shortcut="">
        <help><![CDATA[Alias for --update-with-dependencies]]></help>
      </option>
      <option name="--with-all-dependencies" shortcut="">
        <help><![CDATA[Alias for --update-with-all-dependencies]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages).]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages).]]></help>
      </option>
      <option name="--prefer-stable" shortcut="">
        <help><![CDATA[Prefer stable versions of dependencies (can also be set via the COMPOSER_PREFER_STABLE=1 env var).]]></help>
      </option>
      <option name="--prefer-lowest" shortcut="">
        <help><![CDATA[Prefer lowest versions of dependencies (can also be set via the COMPOSER_PREFER_LOWEST=1 env var).]]></help>
      </option>
      <option name="--minimal-changes" shortcut="-m">
        <help><![CDATA[During an update with -w/-W, only perform absolutely necessary changes to transitive dependencies (can also be set via the COMPOSER_MINIMAL_CHANGES=1 env var).]]></help>
      </option>
      <option name="--sort-packages" shortcut="">
        <help><![CDATA[Sorts packages when adding/updating a new dependency]]></help>
      </option>
      <option name="--optimize-autoloader" shortcut="-o">
        <help><![CDATA[Optimize autoloader during autoloader dump]]></help>
      </option>
      <option name="--classmap-authoritative" shortcut="-a">
        <help><![CDATA[Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.]]></help>
      </option>
      <option name="--apcu-autoloader" shortcut="">
        <help><![CDATA[Use APCu to cache found/not-found classes.]]></help>
      </option>
      <option name="--apcu-autoloader-prefix" shortcut="" pattern="equals">
        <help><![CDATA[Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>run-script</name>
    <help><![CDATA[The <b>run-script</b> command runs scripts defined in composer.json:<br> <br> <b>php composer.phar run-script post-update-cmd</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#run-script-run<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--timeout</td><td></td><td>Sets script timeout in seconds, or 0 for never.</td></tr> <tr><td>--dev</td><td></td><td>Sets the dev mode.</td></tr> <tr><td>--no-dev</td><td></td><td>Disables the dev mode.</td></tr> <tr><td>--list</td><td>(-l)</td><td>List scripts.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>script[=null] args[=null]</params>
    <optionsBefore>
      <option name="--timeout" shortcut="" pattern="equals">
        <help><![CDATA[Sets script timeout in seconds, or 0 for never.]]></help>
      </option>
      <option name="--dev" shortcut="">
        <help><![CDATA[Sets the dev mode.]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables the dev mode.]]></help>
      </option>
      <option name="--list" shortcut="-l">
        <help><![CDATA[List scripts.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>search</name>
    <help><![CDATA[The search command searches for packages by its name<br> <b>php composer.phar search symfony composer</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#search<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--only-name</td><td>(-N)</td><td>Search only in package names</td></tr> <tr><td>--only-vendor</td><td>(-O)</td><td>Search only for vendor / organization names, returns only "vendor" as result</td></tr> <tr><td>--type</td><td>(-t)</td><td>Search for a specific package type</td></tr> <tr><td>--format</td><td>(-f)</td><td>Format of the output: text or json</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>tokens</params>
    <optionsBefore>
      <option name="--only-name" shortcut="-N">
        <help><![CDATA[Search only in package names]]></help>
      </option>
      <option name="--only-vendor" shortcut="-O">
        <help><![CDATA[Search only for vendor / organization names, returns only "vendor" as result]]></help>
      </option>
      <option name="--type" shortcut="-t" pattern="equals">
        <help><![CDATA[Search for a specific package type]]></help>
      </option>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Format of the output: text or json]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>self-update</name>
    <help><![CDATA[The <b>self-update</b> command checks getcomposer.org for newer<br> versions of composer and if found, installs the latest.<br> <br> <b>php composer.phar self-update</b><br> <br> Read more at https://getcomposer.org/doc/03-cli.md#self-update-selfupdate<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--rollback</td><td>(-r)</td><td>Revert to an older installation of composer</td></tr> <tr><td>--clean-backups</td><td></td><td>Delete old backups during an update. This makes the current version of composer the only backup available after the update</td></tr> <tr><td>--no-progress</td><td></td><td>Do not output download progress.</td></tr> <tr><td>--update-keys</td><td></td><td>Prompt user for a key update</td></tr> <tr><td>--stable</td><td></td><td>Force an update to the stable channel</td></tr> <tr><td>--preview</td><td></td><td>Force an update to the preview channel</td></tr> <tr><td>--snapshot</td><td></td><td>Force an update to the snapshot channel</td></tr> <tr><td>--1</td><td></td><td>Force an update to the stable channel, but only use 1.x versions</td></tr> <tr><td>--2</td><td></td><td>Force an update to the stable channel, but only use 2.x versions</td></tr> <tr><td>--2.2</td><td></td><td>Force an update to the stable channel, but only use 2.2.x LTS versions</td></tr> <tr><td>--set-channel-only</td><td></td><td>Only store the channel as the default one and then exit</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>version[=null]</params>
    <optionsBefore>
      <option name="--rollback" shortcut="-r">
        <help><![CDATA[Revert to an older installation of composer]]></help>
      </option>
      <option name="--clean-backups" shortcut="">
        <help><![CDATA[Delete old backups during an update. This makes the current version of composer the only backup available after the update]]></help>
      </option>
      <option name="--no-progress" shortcut="">
        <help><![CDATA[Do not output download progress.]]></help>
      </option>
      <option name="--update-keys" shortcut="">
        <help><![CDATA[Prompt user for a key update]]></help>
      </option>
      <option name="--stable" shortcut="">
        <help><![CDATA[Force an update to the stable channel]]></help>
      </option>
      <option name="--preview" shortcut="">
        <help><![CDATA[Force an update to the preview channel]]></help>
      </option>
      <option name="--snapshot" shortcut="">
        <help><![CDATA[Force an update to the snapshot channel]]></help>
      </option>
      <option name="--1" shortcut="">
        <help><![CDATA[Force an update to the stable channel, but only use 1.x versions]]></help>
      </option>
      <option name="--2" shortcut="">
        <help><![CDATA[Force an update to the stable channel, but only use 2.x versions]]></help>
      </option>
      <option name="--2.2" shortcut="">
        <help><![CDATA[Force an update to the stable channel, but only use 2.2.x LTS versions]]></help>
      </option>
      <option name="--set-channel-only" shortcut="">
        <help><![CDATA[Only store the channel as the default one and then exit]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>show</name>
    <help><![CDATA[The show command displays detailed information about a package, or<br> lists all packages available.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#show-info<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--all</td><td></td><td>List all packages</td></tr> <tr><td>--locked</td><td></td><td>List all locked packages</td></tr> <tr><td>--installed</td><td>(-i)</td><td>List installed packages only (enabled by default, only present for BC).</td></tr> <tr><td>--platform</td><td>(-p)</td><td>List platform packages only</td></tr> <tr><td>--available</td><td>(-a)</td><td>List available packages only</td></tr> <tr><td>--self</td><td>(-s)</td><td>Show the root package information</td></tr> <tr><td>--name-only</td><td>(-N)</td><td>List package names only</td></tr> <tr><td>--path</td><td>(-P)</td><td>Show package paths</td></tr> <tr><td>--tree</td><td>(-t)</td><td>List the dependencies as a tree</td></tr> <tr><td>--latest</td><td>(-l)</td><td>Show the latest version</td></tr> <tr><td>--outdated</td><td>(-o)</td><td>Show the latest version but only for packages that are outdated</td></tr> <tr><td>--ignore</td><td></td><td>Ignore specified package(s). Can contain wildcards (*). Use it with the --outdated option if you don't want to be informed about new versions of some packages.</td></tr> <tr><td>--major-only</td><td>(-M)</td><td>Show only packages that have major SemVer-compatible updates. Use with the --latest or --outdated option.</td></tr> <tr><td>--minor-only</td><td>(-m)</td><td>Show only packages that have minor SemVer-compatible updates. Use with the --latest or --outdated option.</td></tr> <tr><td>--patch-only</td><td></td><td>Show only packages that have patch SemVer-compatible updates. Use with the --latest or --outdated option.</td></tr> <tr><td>--sort-by-age</td><td>(-A)</td><td>Displays the installed version's age, and sorts packages oldest first. Use with the --latest or --outdated option.</td></tr> <tr><td>--direct</td><td>(-D)</td><td>Shows only packages that are directly required by the root package</td></tr> <tr><td>--strict</td><td></td><td>Return a non-zero exit code when there are outdated packages</td></tr> <tr><td>--format</td><td>(-f)</td><td>Format of the output: text or json</td></tr> <tr><td>--no-dev</td><td></td><td>Disables search in require-dev packages.</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages). Use with the --outdated option</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages). Use with the --outdated option</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package[=null] version[=null]</params>
    <optionsBefore>
      <option name="--all" shortcut="">
        <help><![CDATA[List all packages]]></help>
      </option>
      <option name="--locked" shortcut="">
        <help><![CDATA[List all locked packages]]></help>
      </option>
      <option name="--installed" shortcut="-i">
        <help><![CDATA[List installed packages only (enabled by default, only present for BC).]]></help>
      </option>
      <option name="--platform" shortcut="-p">
        <help><![CDATA[List platform packages only]]></help>
      </option>
      <option name="--available" shortcut="-a">
        <help><![CDATA[List available packages only]]></help>
      </option>
      <option name="--self" shortcut="-s">
        <help><![CDATA[Show the root package information]]></help>
      </option>
      <option name="--name-only" shortcut="-N">
        <help><![CDATA[List package names only]]></help>
      </option>
      <option name="--path" shortcut="-P">
        <help><![CDATA[Show package paths]]></help>
      </option>
      <option name="--tree" shortcut="-t">
        <help><![CDATA[List the dependencies as a tree]]></help>
      </option>
      <option name="--latest" shortcut="-l">
        <help><![CDATA[Show the latest version]]></help>
      </option>
      <option name="--outdated" shortcut="-o">
        <help><![CDATA[Show the latest version but only for packages that are outdated]]></help>
      </option>
      <option name="--ignore" shortcut="" pattern="equals">
        <help><![CDATA[Ignore specified package(s). Can contain wildcards (*). Use it with the --outdated option if you don't want to be informed about new versions of some packages.]]></help>
      </option>
      <option name="--major-only" shortcut="-M">
        <help><![CDATA[Show only packages that have major SemVer-compatible updates. Use with the --latest or --outdated option.]]></help>
      </option>
      <option name="--minor-only" shortcut="-m">
        <help><![CDATA[Show only packages that have minor SemVer-compatible updates. Use with the --latest or --outdated option.]]></help>
      </option>
      <option name="--patch-only" shortcut="">
        <help><![CDATA[Show only packages that have patch SemVer-compatible updates. Use with the --latest or --outdated option.]]></help>
      </option>
      <option name="--sort-by-age" shortcut="-A">
        <help><![CDATA[Displays the installed version's age, and sorts packages oldest first. Use with the --latest or --outdated option.]]></help>
      </option>
      <option name="--direct" shortcut="-D">
        <help><![CDATA[Shows only packages that are directly required by the root package]]></help>
      </option>
      <option name="--strict" shortcut="">
        <help><![CDATA[Return a non-zero exit code when there are outdated packages]]></help>
      </option>
      <option name="--format" shortcut="-f" pattern="equals">
        <help><![CDATA[Format of the output: text or json]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables search in require-dev packages.]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages). Use with the --outdated option]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages). Use with the --outdated option]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>status</name>
    <help><![CDATA[The status command displays a list of dependencies that have<br> been modified locally.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#status<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>suggests</name>
    <help><![CDATA[<br> The <b>suggests</b> command shows a sorted list of suggested packages.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#suggests<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--by-package</td><td></td><td>Groups output by suggesting package (default)</td></tr> <tr><td>--by-suggestion</td><td></td><td>Groups output by suggested package</td></tr> <tr><td>--all</td><td>(-a)</td><td>Show suggestions from all dependencies, including transitive ones</td></tr> <tr><td>--list</td><td></td><td>Show only list of suggested package names</td></tr> <tr><td>--no-dev</td><td></td><td>Exclude suggestions from require-dev packages</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--by-package" shortcut="">
        <help><![CDATA[Groups output by suggesting package (default)]]></help>
      </option>
      <option name="--by-suggestion" shortcut="">
        <help><![CDATA[Groups output by suggested package]]></help>
      </option>
      <option name="--all" shortcut="-a">
        <help><![CDATA[Show suggestions from all dependencies, including transitive ones]]></help>
      </option>
      <option name="--list" shortcut="">
        <help><![CDATA[Show only list of suggested package names]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Exclude suggestions from require-dev packages]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>update</name>
    <help><![CDATA[The <b>update</b> command reads the composer.json file from the<br> current directory, processes it, and updates, removes or installs all the<br> dependencies.<br> <br> <b>php composer.phar update</b><br> <br> To limit the update operation to a few packages, you can list the package(s)<br> you want to update as such:<br> <br> <b>php composer.phar update vendor/package1 foo/mypackage [...]</b><br> <br> You may also use an asterisk (*) pattern to limit the update operation to package(s)<br> from a specific vendor:<br> <br> <b>php composer.phar update vendor/package1 foo/* [...]</b><br> <br> To run an update with more restrictive constraints you can use:<br> <br> <b>php composer.phar update --with vendor/package:1.0.*</b><br> <br> To run a partial update with more restrictive constraints you can use the shorthand:<br> <br> <b>php composer.phar update vendor/package:1.0.*</b><br> <br> To select packages names interactively with auto-completion use <b>-i</b>.<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#update-u-upgrade<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--with</td><td></td><td>Temporary version constraint to add, e.g. foo/bar:1.0.0 or foo/bar=1.0.0</td></tr> <tr><td>--prefer-source</td><td></td><td>Forces installation from package sources when possible, including VCS information.</td></tr> <tr><td>--prefer-dist</td><td></td><td>Forces installation from package dist (default behavior).</td></tr> <tr><td>--prefer-install</td><td></td><td>Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).</td></tr> <tr><td>--dry-run</td><td></td><td>Outputs the operations but will not execute anything (implicitly enables --verbose).</td></tr> <tr><td>--dev</td><td></td><td>DEPRECATED: Enables installation of require-dev packages (enabled by default, only present for BC).</td></tr> <tr><td>--no-dev</td><td></td><td>Disables installation of require-dev packages.</td></tr> <tr><td>--lock</td><td></td><td>Overwrites the lock file hash to suppress warning about the lock file being out of date without updating package versions. Package metadata like mirrors and URLs are updated if they changed.</td></tr> <tr><td>--no-install</td><td></td><td>Skip the install step after updating the composer.lock file.</td></tr> <tr><td>--no-audit</td><td></td><td>Skip the audit step after updating the composer.lock file (can also be set via the COMPOSER_NO_AUDIT=1 env var).</td></tr> <tr><td>--audit-format</td><td></td><td>Audit output format. Must be "table", "plain", "json", or "summary".</td></tr> <tr><td>--no-autoloader</td><td></td><td>Skips autoloader generation</td></tr> <tr><td>--no-suggest</td><td></td><td>DEPRECATED: This flag does not exist anymore.</td></tr> <tr><td>--no-progress</td><td></td><td>Do not output download progress.</td></tr> <tr><td>--with-dependencies</td><td>(-w)</td><td>Update also dependencies of packages in the argument list, except those which are root requirements (can also be set via the COMPOSER_WITH_DEPENDENCIES=1 env var).</td></tr> <tr><td>--with-all-dependencies</td><td>(-W)</td><td>Update also dependencies of packages in the argument list, including those which are root requirements (can also be set via the COMPOSER_WITH_ALL_DEPENDENCIES=1 env var).</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--optimize-autoloader</td><td>(-o)</td><td>Optimize autoloader during autoloader dump.</td></tr> <tr><td>--classmap-authoritative</td><td>(-a)</td><td>Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.</td></tr> <tr><td>--apcu-autoloader</td><td></td><td>Use APCu to cache found/not-found classes.</td></tr> <tr><td>--apcu-autoloader-prefix</td><td></td><td>Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader</td></tr> <tr><td>--ignore-platform-req</td><td></td><td>Ignore a specific platform requirement (php & ext- packages).</td></tr> <tr><td>--ignore-platform-reqs</td><td></td><td>Ignore all platform requirements (php & ext- packages).</td></tr> <tr><td>--prefer-stable</td><td></td><td>Prefer stable versions of dependencies (can also be set via the COMPOSER_PREFER_STABLE=1 env var).</td></tr> <tr><td>--prefer-lowest</td><td></td><td>Prefer lowest versions of dependencies (can also be set via the COMPOSER_PREFER_LOWEST=1 env var).</td></tr> <tr><td>--minimal-changes</td><td>(-m)</td><td>During a partial update with -w/-W, only perform absolutely necessary changes to transitive dependencies (can also be set via the COMPOSER_MINIMAL_CHANGES=1 env var).</td></tr> <tr><td>--patch-only</td><td></td><td>Only allow patch version updates for currently installed dependencies.</td></tr> <tr><td>--interactive</td><td>(-i)</td><td>Interactive interface with autocompletion to select the packages to update.</td></tr> <tr><td>--root-reqs</td><td></td><td>Restricts the update to your first degree dependencies.</td></tr> <tr><td>--bump-after-update</td><td></td><td>Runs bump after performing the update.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--with" shortcut="" pattern="equals">
        <help><![CDATA[Temporary version constraint to add, e.g. foo/bar:1.0.0 or foo/bar=1.0.0]]></help>
      </option>
      <option name="--prefer-source" shortcut="">
        <help><![CDATA[Forces installation from package sources when possible, including VCS information.]]></help>
      </option>
      <option name="--prefer-dist" shortcut="">
        <help><![CDATA[Forces installation from package dist (default behavior).]]></help>
      </option>
      <option name="--prefer-install" shortcut="" pattern="equals">
        <help><![CDATA[Forces installation from package dist|source|auto (auto chooses source for dev versions, dist for the rest).]]></help>
      </option>
      <option name="--dry-run" shortcut="">
        <help><![CDATA[Outputs the operations but will not execute anything (implicitly enables --verbose).]]></help>
      </option>
      <option name="--dev" shortcut="">
        <help><![CDATA[DEPRECATED: Enables installation of require-dev packages (enabled by default, only present for BC).]]></help>
      </option>
      <option name="--no-dev" shortcut="">
        <help><![CDATA[Disables installation of require-dev packages.]]></help>
      </option>
      <option name="--lock" shortcut="">
        <help><![CDATA[Overwrites the lock file hash to suppress warning about the lock file being out of date without updating package versions. Package metadata like mirrors and URLs are updated if they changed.]]></help>
      </option>
      <option name="--no-install" shortcut="">
        <help><![CDATA[Skip the install step after updating the composer.lock file.]]></help>
      </option>
      <option name="--no-audit" shortcut="">
        <help><![CDATA[Skip the audit step after updating the composer.lock file (can also be set via the COMPOSER_NO_AUDIT=1 env var).]]></help>
      </option>
      <option name="--audit-format" shortcut="" pattern="equals">
        <help><![CDATA[Audit output format. Must be "table", "plain", "json", or "summary".]]></help>
      </option>
      <option name="--no-autoloader" shortcut="">
        <help><![CDATA[Skips autoloader generation]]></help>
      </option>
      <option name="--no-suggest" shortcut="">
        <help><![CDATA[DEPRECATED: This flag does not exist anymore.]]></help>
      </option>
      <option name="--no-progress" shortcut="">
        <help><![CDATA[Do not output download progress.]]></help>
      </option>
      <option name="--with-dependencies" shortcut="-w">
        <help><![CDATA[Update also dependencies of packages in the argument list, except those which are root requirements (can also be set via the COMPOSER_WITH_DEPENDENCIES=1 env var).]]></help>
      </option>
      <option name="--with-all-dependencies" shortcut="-W">
        <help><![CDATA[Update also dependencies of packages in the argument list, including those which are root requirements (can also be set via the COMPOSER_WITH_ALL_DEPENDENCIES=1 env var).]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--optimize-autoloader" shortcut="-o">
        <help><![CDATA[Optimize autoloader during autoloader dump.]]></help>
      </option>
      <option name="--classmap-authoritative" shortcut="-a">
        <help><![CDATA[Autoload classes from the classmap only. Implicitly enables `--optimize-autoloader`.]]></help>
      </option>
      <option name="--apcu-autoloader" shortcut="">
        <help><![CDATA[Use APCu to cache found/not-found classes.]]></help>
      </option>
      <option name="--apcu-autoloader-prefix" shortcut="" pattern="equals">
        <help><![CDATA[Use a custom prefix for the APCu autoloader cache. Implicitly enables --apcu-autoloader]]></help>
      </option>
      <option name="--ignore-platform-req" shortcut="" pattern="equals">
        <help><![CDATA[Ignore a specific platform requirement (php & ext- packages).]]></help>
      </option>
      <option name="--ignore-platform-reqs" shortcut="">
        <help><![CDATA[Ignore all platform requirements (php & ext- packages).]]></help>
      </option>
      <option name="--prefer-stable" shortcut="">
        <help><![CDATA[Prefer stable versions of dependencies (can also be set via the COMPOSER_PREFER_STABLE=1 env var).]]></help>
      </option>
      <option name="--prefer-lowest" shortcut="">
        <help><![CDATA[Prefer lowest versions of dependencies (can also be set via the COMPOSER_PREFER_LOWEST=1 env var).]]></help>
      </option>
      <option name="--minimal-changes" shortcut="-m">
        <help><![CDATA[During a partial update with -w/-W, only perform absolutely necessary changes to transitive dependencies (can also be set via the COMPOSER_MINIMAL_CHANGES=1 env var).]]></help>
      </option>
      <option name="--patch-only" shortcut="">
        <help><![CDATA[Only allow patch version updates for currently installed dependencies.]]></help>
      </option>
      <option name="--interactive" shortcut="-i">
        <help><![CDATA[Interactive interface with autocompletion to select the packages to update.]]></help>
      </option>
      <option name="--root-reqs" shortcut="">
        <help><![CDATA[Restricts the update to your first degree dependencies.]]></help>
      </option>
      <option name="--bump-after-update" shortcut="" pattern="equals">
        <help><![CDATA[Runs bump after performing the update.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>validate</name>
    <help><![CDATA[The validate command validates a given composer.json and composer.lock<br> <br> Exit codes in case of errors are:<br> 1 validation warning(s), only when --strict is given<br> 2 validation error(s)<br> 3 file unreadable or missing<br> <br> Read more at https://getcomposer.org/doc/03-cli.md#validate<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--no-check-all</td><td></td><td>Do not validate requires for overly strict/loose constraints</td></tr> <tr><td>--check-lock</td><td></td><td>Check if lock file is up to date (even when config.lock is false)</td></tr> <tr><td>--no-check-lock</td><td></td><td>Do not check if lock file is up to date</td></tr> <tr><td>--no-check-publish</td><td></td><td>Do not check for publish errors</td></tr> <tr><td>--no-check-version</td><td></td><td>Do not report a warning if the version field is present</td></tr> <tr><td>--with-dependencies</td><td>(-A)</td><td>Also validate the composer.json of all installed dependencies</td></tr> <tr><td>--strict</td><td></td><td>Return a non-zero exit code for warnings as well as errors</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>file[=null]</params>
    <optionsBefore>
      <option name="--no-check-all" shortcut="">
        <help><![CDATA[Do not validate requires for overly strict/loose constraints]]></help>
      </option>
      <option name="--check-lock" shortcut="">
        <help><![CDATA[Check if lock file is up to date (even when config.lock is false)]]></help>
      </option>
      <option name="--no-check-lock" shortcut="">
        <help><![CDATA[Do not check if lock file is up to date]]></help>
      </option>
      <option name="--no-check-publish" shortcut="">
        <help><![CDATA[Do not check for publish errors]]></help>
      </option>
      <option name="--no-check-version" shortcut="">
        <help><![CDATA[Do not report a warning if the version field is present]]></help>
      </option>
      <option name="--with-dependencies" shortcut="-A">
        <help><![CDATA[Also validate the composer.json of all installed dependencies]]></help>
      </option>
      <option name="--strict" shortcut="">
        <help><![CDATA[Return a non-zero exit code for warnings as well as errors]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>symfony:dump-env</name>
    <help><![CDATA[Compiles .env files to .env.local.php.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--empty</td><td></td><td>Ignore the content of .env files</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>env[=null]</params>
    <optionsBefore>
      <option name="--empty" shortcut="">
        <help><![CDATA[Ignore the content of .env files]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>symfony:recipes</name>
    <help><![CDATA[Shows information about all available recipes.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--outdated</td><td>(-o)</td><td>Show only recipes that are outdated</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package[=null]</params>
    <optionsBefore>
      <option name="--outdated" shortcut="-o">
        <help><![CDATA[Show only recipes that are outdated]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>symfony:recipes:install</name>
    <help><![CDATA[Installs or reinstalls recipes for already installed packages.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td></td><td>Overwrite existing files when a new version of a recipe is available</td></tr> <tr><td>--reset</td><td></td><td>Reset all recipes back to their initial state (should be combined with --force)</td></tr> <tr><td>--yes</td><td></td><td>Answer prompt questions with 'yes' for all questions.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>packages[=null]</params>
    <optionsBefore>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite existing files when a new version of a recipe is available]]></help>
      </option>
      <option name="--reset" shortcut="">
        <help><![CDATA[Reset all recipes back to their initial state (should be combined with --force)]]></help>
      </option>
      <option name="--yes" shortcut="">
        <help><![CDATA[Answer prompt questions with 'yes' for all questions.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>symfony:recipes:update</name>
    <help><![CDATA[Updates an already-installed recipe to the latest version.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--profile</td><td></td><td>Display timing and memory usage information</td></tr> <tr><td>--no-plugins</td><td></td><td>Whether to disable plugins.</td></tr> <tr><td>--no-scripts</td><td></td><td>Skips the execution of all scripts defined in composer.json file.</td></tr> <tr><td>--working-dir</td><td>(-d)</td><td>If specified, use the given directory as working directory.</td></tr> <tr><td>--no-cache</td><td></td><td>Prevent use of the cache</td></tr> </table> <br/>]]></help>
    <params>package[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Display timing and memory usage information]]></help>
      </option>
      <option name="--no-plugins" shortcut="">
        <help><![CDATA[Whether to disable plugins.]]></help>
      </option>
      <option name="--no-scripts" shortcut="">
        <help><![CDATA[Skips the execution of all scripts defined in composer.json file.]]></help>
      </option>
      <option name="--working-dir" shortcut="-d" pattern="equals">
        <help><![CDATA[If specified, use the given directory as working directory.]]></help>
      </option>
      <option name="--no-cache" shortcut="">
        <help><![CDATA[Prevent use of the cache]]></help>
      </option>
    </optionsBefore>
  </command>
</framework>

