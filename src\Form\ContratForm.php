<?php

namespace App\Form;

use App\Entity\Contrat;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;

class ContratForm extends AbstractType
{
  public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('user', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'email', // Show email instead of id
                'label' => 'Employee',
            ])
            ->add('type', ChoiceType::class, [
                'choices' => [
                    'CDI' => 'CDI',
                    'CDD' => 'CDD',
                    'Internship' => 'Internship',
                    'Freelance' => 'Freelance',
                ],
                'placeholder' => 'Select contract type',
                'label' => 'Type of contract',
            ])
            ->add('dateD', DateType::class, [
                'widget' => 'single_text',
                'label' => 'Start Date',
            ])
            ->add('dateF', DateType::class, [
                'widget' => 'single_text',
                'label' => 'End Date',
                'required' => false,
            ])
            ->add('position', null, [
                'label' => 'Position',
            ])
            ->add('salaire', MoneyType::class, [
                'label' => 'Salary',
                'currency' => 'EUR',
                'required' => false,
            ])
            ->add('status', ChoiceType::class, [
                'choices' => [
                    'Active' => 'active',
                    'Terminated' => 'terminated',
                    'Pending' => 'pending',
                ],
                'placeholder' => 'Select status',
                'label' => 'Status',
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Contrat::class,
        ]);
    }
}
