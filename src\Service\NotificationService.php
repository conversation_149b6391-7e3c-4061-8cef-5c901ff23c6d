<?php

namespace App\Service;

use App\Entity\LeaveRequest;
use App\Entity\Notification;
use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;

class NotificationService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserRepository $userRepository
    ) {}

    public function notifyLeaveRequestSubmitted(LeaveRequest $leaveRequest): void
    {
        $employee = $leaveRequest->getEmployee();
        $manager = $leaveRequest->getManager();

        if ($manager) {
            $notification = new Notification();
            $notification->setRecipient($manager);
            $notification->setType(Notification::TYPE_LEAVE_REQUEST_SUBMITTED);
            $notification->setTitle('New Leave Request');
            $notification->setMessage(
                sprintf(
                    '%s has submitted a leave request from %s to %s (%s days)',
                    $employee->getFullName(),
                    $leaveRequest->getStartDate()->format('d/m/Y'),
                    $leaveRequest->getEndDate()->format('d/m/Y'),
                    $leaveRequest->getDaysRequested()
                )
            );
            $notification->setLeaveRequest($leaveRequest);

            $this->entityManager->persist($notification);
        }

        // Notify employee that request was submitted
        $employeeNotification = new Notification();
        $employeeNotification->setRecipient($employee);
        $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_SUBMITTED);
        $employeeNotification->setTitle('Leave Request Submitted');
        $employeeNotification->setMessage('Your leave request has been submitted and is pending manager approval.');
        $employeeNotification->setLeaveRequest($leaveRequest);

        $this->entityManager->persist($employeeNotification);
        $this->entityManager->flush();
    }

    public function notifyManagerDecision(LeaveRequest $leaveRequest, bool $approved): void
    {
        $employee = $leaveRequest->getEmployee();
        
        // Notify employee
        $employeeNotification = new Notification();
        $employeeNotification->setRecipient($employee);
        
        if ($approved) {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_MANAGER_APPROVED);
            $employeeNotification->setTitle('Request Approved by Manager');
            $employeeNotification->setMessage('Your leave request has been approved by your manager. It is now pending HR approval.');
            
            // Notify HR users about the new request that needs their approval
            $hrUsers = $this->userRepository->findByRole('ROLE_RH');
            foreach ($hrUsers as $hrUser) {
                $hrNotification = new Notification();
                $hrNotification->setRecipient($hrUser);
                $hrNotification->setType(Notification::TYPE_LEAVE_REQUEST_MANAGER_APPROVED);
                $hrNotification->setTitle('New Request for HR Approval');
                $hrNotification->setMessage(
                    sprintf(
                        '%s\'s leave request has been approved by manager and requires your approval.',
                        $employee->getFullName()
                    )
                );
                $hrNotification->setLeaveRequest($leaveRequest);
                $this->entityManager->persist($hrNotification);
            }
        } else {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_MANAGER_REJECTED);
            $employeeNotification->setTitle('Request Rejected by Manager');
            $employeeNotification->setMessage('Your leave request has been rejected by your manager.');
        }
        
        $employeeNotification->setLeaveRequest($leaveRequest);
        $this->entityManager->persist($employeeNotification);
        $this->entityManager->flush();
    }

    public function notifyHRDecision(LeaveRequest $leaveRequest, bool $approved): void
    {
        $employee = $leaveRequest->getEmployee();
        $manager = $leaveRequest->getManager();
        
        // Notify employee about the final decision
        $employeeNotification = new Notification();
        $employeeNotification->setRecipient($employee);
        
        if ($approved) {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_HR_APPROVED);
            $employeeNotification->setTitle('Leave Request Approved');
            $employeeNotification->setMessage('Congratulations! Your leave request has been finally approved. The days have been deducted from your balance.');
        } else {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_HR_REJECTED);
            $employeeNotification->setTitle('Leave Request Rejected');
            $employeeNotification->setMessage('Your leave request has been rejected by HR.');
        }
        
        $employeeNotification->setLeaveRequest($leaveRequest);
        $this->entityManager->persist($employeeNotification);

        // Notify manager about the HR decision
        if ($manager) {
            $managerNotification = new Notification();
            $managerNotification->setRecipient($manager);
            $managerNotification->setType($approved ? Notification::TYPE_LEAVE_REQUEST_HR_APPROVED : Notification::TYPE_LEAVE_REQUEST_HR_REJECTED);
            $managerNotification->setTitle($approved ? 'Request Approved by HR' : 'Request Rejected by HR');
            $managerNotification->setMessage(
                sprintf(
                    '%s\'s leave request has been %s by HR.',
                    $employee->getFullName(),
                    $approved ? 'approved' : 'rejected'
                )
            );
            $managerNotification->setLeaveRequest($leaveRequest);
            $this->entityManager->persist($managerNotification);
        }

        $this->entityManager->flush();
    }
}
