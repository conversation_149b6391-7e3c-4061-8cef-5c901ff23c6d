<?php

namespace App\Service;

use App\Entity\LeaveRequest;
use App\Entity\Notification;
use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;

class NotificationService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserRepository $userRepository
    ) {}

    public function notifyLeaveRequestSubmitted(LeaveRequest $leaveRequest): void
    {
        $employee = $leaveRequest->getEmployee();
        $manager = $leaveRequest->getManager();

        if ($manager) {
            $notification = new Notification();
            $notification->setRecipient($manager);
            $notification->setType(Notification::TYPE_LEAVE_REQUEST_SUBMITTED);
            $notification->setTitle('Nouvelle demande de congé');
            $notification->setMessage(
                sprintf(
                    '%s a soumis une demande de congé du %s au %s (%s jours)',
                    $employee->getFullName(),
                    $leaveRequest->getStartDate()->format('d/m/Y'),
                    $leaveRequest->getEndDate()->format('d/m/Y'),
                    $leaveRequest->getDaysRequested()
                )
            );
            $notification->setLeaveRequest($leaveRequest);

            $this->entityManager->persist($notification);
        }

        // Notify employee that request was submitted
        $employeeNotification = new Notification();
        $employeeNotification->setRecipient($employee);
        $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_SUBMITTED);
        $employeeNotification->setTitle('Demande de congé soumise');
        $employeeNotification->setMessage('Votre demande de congé a été soumise et est en attente de validation par votre manager.');
        $employeeNotification->setLeaveRequest($leaveRequest);

        $this->entityManager->persist($employeeNotification);
        $this->entityManager->flush();
    }

    public function notifyManagerDecision(LeaveRequest $leaveRequest, bool $approved): void
    {
        $employee = $leaveRequest->getEmployee();
        
        // Notify employee
        $employeeNotification = new Notification();
        $employeeNotification->setRecipient($employee);
        
        if ($approved) {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_MANAGER_APPROVED);
            $employeeNotification->setTitle('Demande approuvée par le manager');
            $employeeNotification->setMessage('Votre demande de congé a été approuvée par votre manager. Elle est maintenant en attente de validation RH.');
            
            // Notify HR users
            $hrUsers = $this->userRepository->findByRole('ROLE_RH');
            foreach ($hrUsers as $hrUser) {
                $hrNotification = new Notification();
                $hrNotification->setRecipient($hrUser);
                $hrNotification->setType(Notification::TYPE_LEAVE_REQUEST_MANAGER_APPROVED);
                $hrNotification->setTitle('Nouvelle demande à valider');
                $hrNotification->setMessage(
                    sprintf(
                        'La demande de congé de %s a été approuvée par le manager et nécessite votre validation.',
                        $employee->getFullName()
                    )
                );
                $hrNotification->setLeaveRequest($leaveRequest);
                $this->entityManager->persist($hrNotification);
            }
        } else {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_MANAGER_REJECTED);
            $employeeNotification->setTitle('Demande refusée par le manager');
            $employeeNotification->setMessage('Votre demande de congé a été refusée par votre manager.');
        }
        
        $employeeNotification->setLeaveRequest($leaveRequest);
        $this->entityManager->persist($employeeNotification);
        $this->entityManager->flush();
    }

    public function notifyHRDecision(LeaveRequest $leaveRequest, bool $approved): void
    {
        $employee = $leaveRequest->getEmployee();
        $manager = $leaveRequest->getManager();
        
        // Notify employee
        $employeeNotification = new Notification();
        $employeeNotification->setRecipient($employee);
        
        if ($approved) {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_HR_APPROVED);
            $employeeNotification->setTitle('Demande de congé approuvée');
            $employeeNotification->setMessage('Félicitations ! Votre demande de congé a été définitivement approuvée.');
        } else {
            $employeeNotification->setType(Notification::TYPE_LEAVE_REQUEST_HR_REJECTED);
            $employeeNotification->setTitle('Demande de congé refusée');
            $employeeNotification->setMessage('Votre demande de congé a été refusée par les RH.');
        }
        
        $employeeNotification->setLeaveRequest($leaveRequest);
        $this->entityManager->persist($employeeNotification);

        // Notify manager
        if ($manager) {
            $managerNotification = new Notification();
            $managerNotification->setRecipient($manager);
            $managerNotification->setType($approved ? Notification::TYPE_LEAVE_REQUEST_HR_APPROVED : Notification::TYPE_LEAVE_REQUEST_HR_REJECTED);
            $managerNotification->setTitle($approved ? 'Demande approuvée par RH' : 'Demande refusée par RH');
            $managerNotification->setMessage(
                sprintf(
                    'La demande de congé de %s a été %s par les RH.',
                    $employee->getFullName(),
                    $approved ? 'approuvée' : 'refusée'
                )
            );
            $managerNotification->setLeaveRequest($leaveRequest);
            $this->entityManager->persist($managerNotification);
        }

        $this->entityManager->flush();
    }
}
