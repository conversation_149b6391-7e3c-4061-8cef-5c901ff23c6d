<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use App\Entity\User;
use App\Entity\LeaveRequest;
use Doctrine\ORM\EntityManagerInterface;

class LeaveRequestControllerTest extends WebTestCase
{
    private $client;
    private $entityManager;

    protected function setUp(): void
    {
        $this->client = static::createClient();
        $this->entityManager = $this->client->getContainer()
            ->get('doctrine')
            ->getManager();
    }

    public function testManagerCanViewRequestAndNavigateBack(): void
    {
        // Create a manager user
        $manager = new User();
        $manager->setEmail('<EMAIL>');
        $manager->setRoles(['ROLE_MANAGER']);
        $manager->setPassword('password');
        $manager->setFirstName('Manager');
        $manager->setLastName('Test');
        
        // Create an employee user
        $employee = new User();
        $employee->setEmail('<EMAIL>');
        $employee->setRoles(['ROLE_EMPLOYE']);
        $employee->setPassword('password');
        $employee->setFirstName('Employee');
        $employee->setLastName('Test');
        $employee->setManager($manager);
        
        $this->entityManager->persist($manager);
        $this->entityManager->persist($employee);
        
        // Create a leave request
        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setManager($manager);
        $leaveRequest->setStartDate(new \DateTime('+1 week'));
        $leaveRequest->setEndDate(new \DateTime('+1 week +2 days'));
        $leaveRequest->setDaysRequested(3);
        $leaveRequest->setType(LeaveRequest::TYPE_ANNUAL);
        $leaveRequest->setReason('Test vacation');
        $leaveRequest->setStatus(LeaveRequest::STATUS_PENDING);
        $leaveRequest->setCreatedAt(new \DateTime());
        
        $this->entityManager->persist($leaveRequest);
        $this->entityManager->flush();
        
        // Login as manager
        $this->client->loginUser($manager);
        
        // Test manager pending page
        $crawler = $this->client->request('GET', '/leave-request/manager/pending');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Demandes de congé en attente');
        
        // Test that the "Voir" button exists
        $this->assertSelectorExists('a[href*="/leave-request/' . $leaveRequest->getId() . '"]');
        
        // Click on the "Voir" button to view the request
        $viewLink = $crawler->filter('a[href*="/leave-request/' . $leaveRequest->getId() . '"]')->first();
        $crawler = $this->client->click($viewLink->link());
        
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h3', 'Détails de la demande de congé');
        
        // Test that the back button exists and points to manager pending
        $this->assertSelectorExists('a[href*="/leave-request/manager/pending"]');
        
        // Test that the decision button exists for pending requests
        $this->assertSelectorExists('a[href*="/leave-request/' . $leaveRequest->getId() . '/manager-decision"]');
    }

    public function testShowPageBackUrlBasedOnReferer(): void
    {
        // Create test data
        $manager = new User();
        $manager->setEmail('<EMAIL>');
        $manager->setRoles(['ROLE_MANAGER']);
        $manager->setPassword('password');
        $manager->setFirstName('Manager');
        $manager->setLastName('Test2');
        
        $employee = new User();
        $employee->setEmail('<EMAIL>');
        $employee->setRoles(['ROLE_EMPLOYE']);
        $employee->setPassword('password');
        $employee->setFirstName('Employee');
        $employee->setLastName('Test2');
        $employee->setManager($manager);
        
        $this->entityManager->persist($manager);
        $this->entityManager->persist($employee);
        
        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setManager($manager);
        $leaveRequest->setStartDate(new \DateTime('+1 week'));
        $leaveRequest->setEndDate(new \DateTime('+1 week +2 days'));
        $leaveRequest->setDaysRequested(3);
        $leaveRequest->setType(LeaveRequest::TYPE_ANNUAL);
        $leaveRequest->setReason('Test vacation');
        $leaveRequest->setStatus(LeaveRequest::STATUS_PENDING);
        $leaveRequest->setCreatedAt(new \DateTime());
        
        $this->entityManager->persist($leaveRequest);
        $this->entityManager->flush();
        
        // Login as manager
        $this->client->loginUser($manager);
        
        // Test accessing show page with manager/pending referer
        $this->client->request('GET', '/leave-request/' . $leaveRequest->getId(), [], [], [
            'HTTP_REFERER' => 'http://localhost/leave-request/manager/pending'
        ]);
        
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('a[href*="/leave-request/manager/pending"]');
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        
        // Clean up test data
        $this->entityManager->close();
        $this->entityManager = null;
    }
}
