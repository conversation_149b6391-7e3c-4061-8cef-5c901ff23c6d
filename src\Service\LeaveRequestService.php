<?php

namespace App\Service;

use App\Entity\LeaveRequest;
use App\Entity\User;
use App\Repository\LeaveRequestRepository;
use Doctrine\ORM\EntityManagerInterface;

class LeaveRequestService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private LeaveRequestRepository $leaveRequestRepository,
        private NotificationService $notificationService
    ) {}

    public function submitLeaveRequest(LeaveRequest $leaveRequest): void
    {
        // Calculate days requested
        $startDate = $leaveRequest->getStartDate();
        $endDate = $leaveRequest->getEndDate();
        $daysRequested = $this->calculateWorkingDays($startDate, $endDate);
        $leaveRequest->setDaysRequested($daysRequested);

        // Set manager
        $employee = $leaveRequest->getEmployee();
        $leaveRequest->setManager($employee->getManager());

        $this->entityManager->persist($leaveRequest);
        $this->entityManager->flush();

        // Send notifications
        $this->notificationService->notifyLeaveRequestSubmitted($leaveRequest);
    }

    public function approveByManager(LeaveRequest $leaveRequest, ?string $comment = null): void
    {
        $leaveRequest->setStatus(LeaveRequest::STATUS_MANAGER_APPROVED);
        $leaveRequest->setManagerComment($comment);
        $leaveRequest->setManagerDecisionAt(new \DateTime());

        $this->entityManager->flush();

        $this->notificationService->notifyManagerDecision($leaveRequest, true);
    }

    public function rejectByManager(LeaveRequest $leaveRequest, ?string $comment = null): void
    {
        $leaveRequest->setStatus(LeaveRequest::STATUS_MANAGER_REJECTED);
        $leaveRequest->setManagerComment($comment);
        $leaveRequest->setManagerDecisionAt(new \DateTime());

        $this->entityManager->flush();

        $this->notificationService->notifyManagerDecision($leaveRequest, false);
    }

    public function approveByHR(LeaveRequest $leaveRequest, ?string $comment = null): void
    {
        $leaveRequest->setStatus(LeaveRequest::STATUS_HR_APPROVED);
        $leaveRequest->setHrComment($comment);
        $leaveRequest->setHrDecisionAt(new \DateTime());

        // Deduct days from employee's leave balance
        $employee = $leaveRequest->getEmployee();
        $currentBalance = $employee->getSoldeConge() ?? 0;
        $newBalance = $currentBalance - $leaveRequest->getDaysRequested();
        $employee->setSoldeConge($newBalance);

        $this->entityManager->flush();

        $this->notificationService->notifyHRDecision($leaveRequest, true);
    }

    public function rejectByHR(LeaveRequest $leaveRequest, ?string $comment = null): void
    {
        $leaveRequest->setStatus(LeaveRequest::STATUS_HR_REJECTED);
        $leaveRequest->setHrComment($comment);
        $leaveRequest->setHrDecisionAt(new \DateTime());

        $this->entityManager->flush();

        $this->notificationService->notifyHRDecision($leaveRequest, false);
    }

    public function canEmployeeRequestLeave(User $employee, float $daysRequested): bool
    {
        $currentBalance = $employee->getSoldeConge() ?? 0;
        return $currentBalance >= $daysRequested;
    }

    public function hasConflictingLeave(User $employee, \DateTimeInterface $startDate, \DateTimeInterface $endDate): bool
    {
        $conflictingRequests = $this->leaveRequestRepository->findApprovedByEmployeeAndDateRange(
            $employee,
            $startDate,
            $endDate
        );

        return count($conflictingRequests) > 0;
    }

    public function calculateWorkingDays(\DateTimeInterface $startDate, \DateTimeInterface $endDate): float
    {
        $days = 0;
        $current = clone $startDate;

        while ($current <= $endDate) {
            // Skip weekends (Saturday = 6, Sunday = 0)
            if ($current->format('w') != 0 && $current->format('w') != 6) {
                $days++;
            }
            $current->modify('+1 day');
        }

        return $days;
    }

    public function validateLeaveRequest(LeaveRequest $leaveRequest): array
    {
        $errors = [];
        
        // Check if start date is in the future
        if ($leaveRequest->getStartDate() <= new \DateTime()) {
            $errors[] = 'La date de début doit être dans le futur.';
        }
        
        // Check if end date is after start date
        if ($leaveRequest->getEndDate() < $leaveRequest->getStartDate()) {
            $errors[] = 'La date de fin doit être postérieure à la date de début.';
        }
        
        // Check weekend dates
        if ($this->isWeekend($leaveRequest->getStartDate()) || 
            $this->isWeekend($leaveRequest->getEndDate())) {
            $errors[] = 'Les dates de congé ne peuvent pas être sur un week-end.';
        }
        
        return $errors;
    }

    private function isWeekend(\DateTimeInterface $date): bool
    {
        return in_array($date->format('w'), [0, 6]);
    }
}
