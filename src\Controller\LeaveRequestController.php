<?php

namespace App\Controller;

use App\Entity\LeaveRequest;
use App\Form\LeaveRequestDecisionForm;
use App\Form\LeaveRequestForm;
use App\Repository\LeaveRequestRepository;
use App\Service\LeaveRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/leave-request')]
class LeaveRequestController extends AbstractController
{
    public function __construct(
        private LeaveRequestService $leaveRequestService,
        private LeaveRequestRepository $leaveRequestRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/', name: 'app_leave_request_index', methods: ['GET'])]
    #[IsGranted('ROLE_EMPLOYEE')]
    public function index(): Response
    {
        $user = $this->getUser();
        $leaveRequests = $this->leaveRequestRepository->findByEmployee($user);

        return $this->render('leave_request/index.html.twig', [
            'leave_requests' => $leaveRequests,
            'user' => $user,
        ]);
    }

    #[Route('/new', name: 'app_leave_request_new', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_EMPLOYEE')]
    public function new(Request $request): Response
    {
        $user = $this->getUser();
        
        // Check if user has a manager assigned
        if (!$user->getManager()) {
            return $this->render('leave_request/new.html.twig', [
                'leave_request' => new LeaveRequest(),
                'form' => $this->createForm(LeaveRequestForm::class, new LeaveRequest()),
                'has_manager' => false,
                'current_balance' => $user->getSoldeConge(),
            ]);
        }

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($user);
        $leaveRequest->setStatus(LeaveRequest::STATUS_PENDING);
        
        $form = $this->createForm(LeaveRequestForm::class, $leaveRequest);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Calculate working days
            $daysRequested = $this->leaveRequestService->calculateWorkingDays(
                $leaveRequest->getStartDate(),
                $leaveRequest->getEndDate()
            );
            
            // Set days requested
            $leaveRequest->setDaysRequested($daysRequested);
            
            // Validate using the service
            $validationErrors = $this->leaveRequestService->validateLeaveRequest($leaveRequest);
            
            if (!empty($validationErrors)) {
                foreach ($validationErrors as $error) {
                    $this->addFlash('error', $error);
                }
                
                return $this->render('leave_request/new.html.twig', [
                    'leave_request' => $leaveRequest,
                    'form' => $form,
                    'has_manager' => true,
                    'current_balance' => $user->getSoldeConge() ?? 0,
                ]);
            }

            // Check if employee has enough leave balance
            if (!$this->leaveRequestService->canEmployeeRequestLeave($user, $daysRequested)) {
                $this->addFlash('error', 'Solde de congés insuffisant pour cette demande.');
                return $this->render('leave_request/new.html.twig', [
                    'leave_request' => $leaveRequest,
                    'form' => $form,
                    'has_manager' => true,
                    'current_balance' => $user->getSoldeConge() ?? 0,
                ]);
            }

            // Check for conflicting leave
            if ($this->leaveRequestService->hasConflictingLeave($user, $leaveRequest->getStartDate(), $leaveRequest->getEndDate())) {
                $this->addFlash('error', 'Vous avez déjà des congés approuvés sur cette période.');
                return $this->render('leave_request/new.html.twig', [
                    'leave_request' => $leaveRequest,
                    'form' => $form,
                    'has_manager' => true,
                    'current_balance' => $user->getSoldeConge() ?? 0,
                ]);
            }

            try {
                $this->leaveRequestService->submitLeaveRequest($leaveRequest);
                $this->addFlash('success', 'Votre demande de congé a été soumise avec succès.');
                
                return $this->redirectToRoute('app_leave_request_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Une erreur est survenue lors de l\'enregistrement: ' . $e->getMessage());
                
                return $this->render('leave_request/new.html.twig', [
                    'leave_request' => $leaveRequest,
                    'form' => $form,
                    'has_manager' => true,
                    'current_balance' => $user->getSoldeConge() ?? 0,
                ]);
            }
        } elseif ($form->isSubmitted()) {
            // Form was submitted but not valid
            $this->addFlash('error', 'Veuillez corriger les erreurs dans le formulaire.');
        }

        return $this->render('leave_request/new.html.twig', [
            'leave_request' => $leaveRequest,
            'form' => $form,
            'has_manager' => true,
            'current_balance' => $user->getSoldeConge() ?? 0,
        ]);
    }

    #[Route('/{id}', name: 'app_leave_request_show', methods: ['GET'])]
    #[IsGranted('ROLE_EMPLOYEE')]
    public function show(LeaveRequest $leaveRequest): Response
    {
        $user = $this->getUser();
        
        // Check if user can view this leave request
        if ($leaveRequest->getEmployee() !== $user && 
            $leaveRequest->getManager() !== $user && 
            !$user->hasRole('ROLE_RH')) {
            throw $this->createAccessDeniedException();
        }

        return $this->render('leave_request/show.html.twig', [
            'leave_request' => $leaveRequest,
        ]);
    }

    #[Route('/manager/pending', name: 'app_leave_request_manager_pending', methods: ['GET'])]
    #[IsGranted('ROLE_MANAGER')]
    public function managerPending(): Response
    {
        $user = $this->getUser();
        $pendingRequests = $this->leaveRequestRepository->findPendingForManager($user);

        return $this->render('leave_request/manager_pending.html.twig', [
            'pending_requests' => $pendingRequests,
        ]);
    }

    #[Route('/manager/all', name: 'app_leave_request_manager_all', methods: ['GET'])]
    #[IsGranted('ROLE_MANAGER')]
    public function managerAll(): Response
    {
        $user = $this->getUser();
        $allRequests = $this->leaveRequestRepository->findAllForManager($user);

        return $this->render('leave_request/manager_all.html.twig', [
            'all_requests' => $allRequests,
        ]);
    }

    #[Route('/{id}/manager-decision', name: 'app_leave_request_manager_decision', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function managerDecision(Request $request, LeaveRequest $leaveRequest): Response
    {
        $user = $this->getUser();
        
        if ($leaveRequest->getManager() !== $user) {
            throw $this->createAccessDeniedException();
        }

        if (!$leaveRequest->isPending()) {
            $this->addFlash('error', 'Cette demande a déjà été traitée.');
            return $this->redirectToRoute('app_leave_request_manager_pending');
        }

        $form = $this->createForm(LeaveRequestDecisionForm::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $decision = $data['decision'];
            $comment = $data['comment'];

            if ($decision === 'approve') {
                $this->leaveRequestService->approveByManager($leaveRequest, $comment);
                $this->addFlash('success', 'Demande approuvée avec succès.');
            } else {
                $this->leaveRequestService->rejectByManager($leaveRequest, $comment);
                $this->addFlash('success', 'Demande refusée.');
            }

            return $this->redirectToRoute('app_leave_request_manager_pending');
        }

        return $this->render('leave_request/manager_decision.html.twig', [
            'leave_request' => $leaveRequest,
            'form' => $form,
        ]);
    }

    #[Route('/hr/pending', name: 'app_leave_request_hr_pending', methods: ['GET'])]
    #[IsGranted('ROLE_RH')]
    public function hrPending(): Response
    {
        $pendingRequests = $this->leaveRequestRepository->findPendingForHR();

        return $this->render('leave_request/hr_pending.html.twig', [
            'pending_requests' => $pendingRequests,
        ]);
    }

    #[Route('/hr/all', name: 'app_leave_request_hr_all', methods: ['GET'])]
    #[IsGranted('ROLE_RH')]
    public function hrAll(): Response
    {
        $allRequests = $this->leaveRequestRepository->findAllForHR();

        return $this->render('leave_request/hr_all.html.twig', [
            'all_requests' => $allRequests,
        ]);
    }

    #[Route('/{id}/hr-decision', name: 'app_leave_request_hr_decision', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_RH')]
    public function hrDecision(Request $request, LeaveRequest $leaveRequest): Response
    {
        if (!$leaveRequest->isManagerApproved()) {
            $this->addFlash('error', 'Cette demande n\'a pas encore été approuvée par le manager.');
            return $this->redirectToRoute('app_leave_request_hr_pending');
        }

        $form = $this->createForm(LeaveRequestDecisionForm::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $decision = $data['decision'];
            $comment = $data['comment'];

            if ($decision === 'approve') {
                $this->leaveRequestService->approveByHR($leaveRequest, $comment);
                $this->addFlash('success', 'Demande approuvée définitivement.');
            } else {
                $this->leaveRequestService->rejectByHR($leaveRequest, $comment);
                $this->addFlash('success', 'Demande refusée.');
            }

            return $this->redirectToRoute('app_leave_request_hr_pending');
        }

        return $this->render('leave_request/hr_decision.html.twig', [
            'leave_request' => $leaveRequest,
            'form' => $form,
        ]);
    }
}
