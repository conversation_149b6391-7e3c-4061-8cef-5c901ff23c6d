<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250707151344 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE contrat ADD user_id INT DEFAULT NULL, ADD type VARCHAR(255) DEFAULT NULL, ADD date_f DATE DEFAULT NULL, ADD position VARCHAR(255) DEFAULT NULL, ADD salaire DOUBLE PRECISION DEFAULT NULL, ADD status VARCHAR(255) DEFAULT NULL, ADD description VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contrat ADD CONSTRAINT FK_60349993A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_60349993A76ED395 ON contrat (user_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE contrat DROP FOREIGN KEY FK_60349993A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_60349993A76ED395 ON contrat
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contrat DROP user_id, DROP type, DROP date_f, DROP position, DROP salaire, DROP status, DROP description
        SQL);
    }
}
