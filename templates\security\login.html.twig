{% extends 'base.html.twig' %}

{% block title %}Sign Up{% endblock %}

{% block body %}
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <!-- Spinner -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>

        <!-- Sign Up - Container -->
        <div class="container-fluid px-5">
            <div class="row h-200 align-items-center justify-content-center" style="min-height: 100vh;">
                <div class="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
                    <div class="bg-light rounded p-4 p-sm-5 my-4 mx-3">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <h3 class="text-primary me-3"><i class="fa fa-hashtag me-2"></i>RhManagement</h3>
                            <h3>Sign Up</h3>
                        </div>

                        <form method="post">
                            {% if error %}
                                <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
                            {% endif %}

                            <div class="form-floating mb-3">
                                <input type="email" value="{{ last_username }}" name="email" id="floatingInput" class="form-control" placeholder="<EMAIL>" required autofocus>
                                <label for="floatingInput">Email address</label>
                            </div>
                            <div class="form-floating mb-4">
                                <input type="password" name="password" id="floatingPassword" class="form-control" placeholder="Password" required>
                                <label for="floatingPassword">Password</label>
                            </div>
                            <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                            <button type="submit" class="btn btn-primary py-3 w-100 mb-4">Sign In</button>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SweetAlert2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Flash Message Alert -->
    {% for label, messages in app.flashes %}
        {% for message in messages %}
            <script>
                Swal.fire({
                    icon: '{{ label == "success" ? "success" : "error" }}',
                    title: '{{ label|capitalize }}',
                    text: '{{ message }}',
                    confirmButtonColor: '#3085d6'
                });
            </script>
        {% endfor %}
    {% endfor %}
{% endblock %}
