{% extends 'base.html.twig' %}

{% block title %}Décision Manager{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Décision sur la demande de congé</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Détails de la demande</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Employé:</strong></td>
                                    <td>{{ leave_request.employee.fullName }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Type:</strong></td>
                                    <td>
                                        {% if leave_request.type == 'annual' %}
                                            Congé annuel
                                        {% elseif leave_request.type == 'sick' %}
                                            Congé maladie
                                        {% elseif leave_request.type == 'maternity' %}
                                            Congé maternité
                                        {% elseif leave_request.type == 'paternity' %}
                                            Congé paternité
                                        {% else %}
                                            Autre
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Période:</strong></td>
                                    <td>{{ leave_request.startDate|date('d/m/Y') }} - {{ leave_request.endDate|date('d/m/Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Nombre de jours:</strong></td>
                                    <td>{{ leave_request.daysRequested }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Solde employé:</strong></td>
                                    <td>{{ leave_request.employee.soldeConge ?? 0 }} jours</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            {% if leave_request.reason %}
                                <h5>Motif</h5>
                                <div class="alert alert-light">
                                    {{ leave_request.reason }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <hr>

                    <h5>Votre décision</h5>
                    {{ form_start(form) }}
                        <div class="mb-3">
                            {{ form_label(form.decision) }}
                            <div class="mt-2">
                                {{ form_widget(form.decision) }}
                            </div>
                            {{ form_errors(form.decision) }}
                        </div>

                        <div class="mb-3">
                            {{ form_label(form.comment) }}
                            {{ form_widget(form.comment) }}
                            {{ form_errors(form.comment) }}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ path('app_leave_request_manager_pending') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check"></i> Confirmer la décision
                            </button>
                        </div>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}