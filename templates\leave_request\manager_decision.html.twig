{% extends 'layout.html.twig' %}

{% block title %}Manager Decision{% endblock %}

{% block stylesheets %}
<style>
.decision-card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.decision-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.info-table td {
    padding: 0.75rem 0.5rem;
    border: none;
}

.info-table td:first-child {
    font-weight: 600;
    color: #495057;
    width: 40%;
}

.decision-section {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.btn-decision {
    min-width: 120px;
    font-weight: 500;
}
</style>
{% endblock %}

{% block body %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card decision-card">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-gavel me-2"></i>Leave Request Decision</h3>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-info-circle me-2 text-primary"></i>Request Details</h5>
                            <table class="table info-table">
                                <tr>
                                    <td>Employee:</td>
                                    <td>{{ leave_request.employee.fullName }}</td>
                                </tr>
                                <tr>
                                    <td>Type:</td>
                                    <td>
                                        {% if leave_request.type == 'annual' %}
                                            <span class="badge bg-primary">Annual Leave</span>
                                        {% elseif leave_request.type == 'sick' %}
                                            <span class="badge bg-warning">Sick Leave</span>
                                        {% elseif leave_request.type == 'maternity' %}
                                            <span class="badge bg-info">Maternity Leave</span>
                                        {% elseif leave_request.type == 'paternity' %}
                                            <span class="badge bg-info">Paternity Leave</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Other</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td>Period:</td>
                                    <td>{{ leave_request.startDate|date('d/m/Y') }} - {{ leave_request.endDate|date('d/m/Y') }}</td>
                                </tr>
                                <tr>
                                    <td>Days Requested:</td>
                                    <td><span class="badge bg-info fs-6">{{ leave_request.daysRequested }} days</span></td>
                                </tr>
                                <tr>
                                    <td>Employee Balance:</td>
                                    <td><span class="badge bg-success fs-6">{{ leave_request.employee.soldeConge ?? 0 }} days</span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            {% if leave_request.reason %}
                                <h5><i class="fas fa-comment me-2 text-primary"></i>Reason</h5>
                                <div class="alert alert-light border-start border-primary border-4">
                                    <i class="fas fa-quote-left text-muted me-2"></i>
                                    {{ leave_request.reason }}
                                </div>
                            {% else %}
                                <div class="text-center text-muted mt-4">
                                    <i class="fas fa-comment-slash fa-2x mb-2"></i>
                                    <p>No reason provided</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="decision-section">
                        <h5><i class="fas fa-gavel me-2 text-primary"></i>Your Decision</h5>
                        {{ form_start(form) }}
                            <div class="mb-4">
                                {{ form_label(form.decision, 'Decision', {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                <div class="mt-2">
                                    {{ form_widget(form.decision, {'attr': {'class': 'form-check-input me-2'}}) }}
                                </div>
                                {{ form_errors(form.decision) }}
                            </div>

                            <div class="mb-4">
                                {{ form_label(form.comment, 'Comment (Optional)', {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                {{ form_widget(form.comment, {'attr': {'class': 'form-control', 'rows': 4, 'placeholder': 'Add your comments here...'}}) }}
                                {{ form_errors(form.comment) }}
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ path('app_leave_request_manager_pending') }}" class="btn btn-secondary btn-decision">
                                    <i class="fas fa-arrow-left me-2"></i>Back
                                </a>
                                <button type="submit" class="btn btn-primary btn-decision">
                                    <i class="fas fa-check me-2"></i>Confirm Decision
                                </button>
                            </div>
                        {{ form_end(form) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}