<?php

namespace App\Entity;

use App\Repository\LeaveRequestRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LeaveRequestRepository::class)]
class LeaveRequest
{
    public const STATUS_PENDING = 'pending';
    public const STATUS_MANAGER_APPROVED = 'manager_approved';
    public const STATUS_MANAGER_REJECTED = 'manager_rejected';
    public const STATUS_HR_APPROVED = 'hr_approved';
    public const STATUS_HR_REJECTED = 'hr_rejected';

    public const TYPE_ANNUAL = 'annual';
    public const TYPE_SICK = 'sick';
    public const TYPE_MATERNITY = 'maternity';
    public const TYPE_PATERNITY = 'paternity';
    public const TYPE_OTHER = 'other';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $employee = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $manager = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $startDate = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $endDate = null;

    #[ORM\Column]
    private ?float $daysRequested = null;

    #[ORM\Column(length: 50)]
    private ?string $type = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $reason = null;

    #[ORM\Column(length: 50)]
    private ?string $status = self::STATUS_PENDING;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $managerComment = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $hrComment = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $managerDecisionAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $hrDecisionAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?User
    {
        return $this->employee;
    }

    public function setEmployee(?User $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getManager(): ?User
    {
        return $this->manager;
    }

    public function setManager(?User $manager): static
    {
        $this->manager = $manager;
        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeInterface $startDate): static
    {
        $this->startDate = $startDate;
        return $this;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeInterface $endDate): static
    {
        $this->endDate = $endDate;
        return $this;
    }

    public function getDaysRequested(): ?float
    {
        return $this->daysRequested;
    }

    public function setDaysRequested(float $daysRequested): static
    {
        $this->daysRequested = $daysRequested;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function setReason(?string $reason): static
    {
        $this->reason = $reason;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getManagerComment(): ?string
    {
        return $this->managerComment;
    }

    public function setManagerComment(?string $managerComment): static
    {
        $this->managerComment = $managerComment;
        return $this;
    }

    public function getHrComment(): ?string
    {
        return $this->hrComment;
    }

    public function setHrComment(?string $hrComment): static
    {
        $this->hrComment = $hrComment;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getManagerDecisionAt(): ?\DateTimeInterface
    {
        return $this->managerDecisionAt;
    }

    public function setManagerDecisionAt(?\DateTimeInterface $managerDecisionAt): static
    {
        $this->managerDecisionAt = $managerDecisionAt;
        return $this;
    }

    public function getHrDecisionAt(): ?\DateTimeInterface
    {
        return $this->hrDecisionAt;
    }

    public function setHrDecisionAt(?\DateTimeInterface $hrDecisionAt): static
    {
        $this->hrDecisionAt = $hrDecisionAt;
        return $this;
    }

    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isManagerApproved(): bool
    {
        return $this->status === self::STATUS_MANAGER_APPROVED;
    }

    public function isManagerRejected(): bool
    {
        return $this->status === self::STATUS_MANAGER_REJECTED;
    }

    public function isHrApproved(): bool
    {
        return $this->status === self::STATUS_HR_APPROVED;
    }

    public function isHrRejected(): bool
    {
        return $this->status === self::STATUS_HR_REJECTED;
    }

    public function isFinallyApproved(): bool
    {
        return $this->status === self::STATUS_HR_APPROVED;
    }

    public function isFinallyRejected(): bool
    {
        return in_array($this->status, [self::STATUS_MANAGER_REJECTED, self::STATUS_HR_REJECTED]);
    }
}
