{% extends 'base.html.twig' %}

{% block title %}Employee Details - {{ user.firstname }} {{ user.lastname }}{% endblock %}

{% block body %}
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <div class="content">
            <!-- Breadcrumb Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="row">
                    <div class="col-12">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb bg-light rounded p-3 mb-4">
                                <li class="breadcrumb-item">
                                    <a href="{{ path('rh_dashboard') }}" class="text-decoration-none">
                                        <i class="fa fa-home me-1"></i>Dashboard
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="{{ path('app_user_index') }}" class="text-decoration-none">
                                        <i class="fa fa-users me-1"></i>Employees
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <i class="fa fa-user me-1"></i>{{ user.firstname }} {{ user.lastname }}
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
            <!-- Breadcrumb End -->

            <!-- Profile Header -->
            <div class="container-fluid px-4">
                <div class="row g-4">
                    <div class="col-12">
                        <div class="bg-light rounded p-4 mb-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <div class="position-relative me-4">
                                        <img class="rounded-circle border border-3 border-white shadow"
                                             src="{{ asset('assets/img/user.jpg') }}"
                                             alt="Employee Photo"
                                             style="width: 100px; height: 100px;">
                                    </div>
                                    <div>
                                        <h3 class="mb-1 text-primary">{{ user.firstname }} {{ user.lastname }}</h3>
                                        <p class="mb-1 text-muted">
                                            <i class="fa fa-briefcase me-2"></i>
                                            {{ user.poste ?: 'Not defined' }}
                                        </p>
                                        <p class="mb-0 text-muted">
                                            <i class="fa fa-envelope me-2"></i>
                                            <a href="mailto:{{ user.email }}" class="text-decoration-none">{{ user.email }}</a>
                                        </p>
                                    </div>
                                </div>
                                <div class="d-flex gap-2">
                                    <a href="{{ path('app_user_edit', {'id': user.id}) }}" class="btn btn-warning">
                                        <i class="fa fa-edit me-2"></i>Edit
                                    </a>
                                    <a href="{{ path('app_user_index') }}" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left me-2"></i>Back to List
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Cards -->
            <div class="container-fluid px-4">
                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="bg-light rounded h-100 p-4">
                            <h5 class="mb-4 text-primary">
                                <i class="fa fa-id-card me-2"></i>Personal Information
                            </h5>
                            <div class="mb-3"><strong>ID:</strong> #{{ user.id }}</div>
                            <div class="mb-3"><strong>First Name:</strong> {{ user.firstname }}</div>
                            <div class="mb-3"><strong>Last Name:</strong> {{ user.lastname }}</div>
                            <div class="mb-3">
                                <strong>Email:</strong>
                                <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                            </div>
                            <div class="mb-3">
                                <strong>Roles:</strong>
                                {% for role in user.roles %}
                                    <span class="badge bg-info me-1">{{ role }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="bg-light rounded h-100 p-4">
                            <h5 class="mb-4 text-primary">
                                <i class="fa fa-briefcase me-2"></i>Professional Information
                            </h5>
                            <div class="mb-3">
                                <strong>Position:</strong> {{ user.poste ?: 'Not defined' }}
                            </div>
                            <div class="mb-3">
                                <strong>Hire Date:</strong>
                                {% if user.dateEmbauche %}
                                    {{ user.dateEmbauche|date('d/m/Y') }}
                                {% else %}
                                    Not defined
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <strong>Trial Period End:</strong>
                                {% if user.dateFinPeriodeEssai %}
                                    {{ user.dateFinPeriodeEssai|date('d/m/Y') }}
                                {% else %}
                                    Not defined
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <strong>Gross Salary:</strong>
                                {% if user.salaireBrut %}
                                    {{ user.salaireBrut|number_format(2, ',', ' ') }} €
                                {% else %}
                                    Not defined
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <strong>Leave Balance:</strong>
                                {% if user.soldeConge is not null %}
                                    {{ user.soldeConge }} days
                                {% else %}
                                    Not defined
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="container-fluid pt-4 px-4">
                <div class="row g-4">
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-primary rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-calendar-check fa-3x text-white"></i>
                            <div class="ms-3 text-white">
                                <p class="mb-2">Days Since Hire</p>
                                <h6 class="mb-0">{{ daysSinceHire ?? 0 }}</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-success rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-euro-sign fa-3x text-white"></i>
                            <div class="ms-3 text-white">
                                <p class="mb-2">Monthly Salary</p>
                                <h6 class="mb-0">
                                    {% if user.salaireBrut %}
                                        {{ user.salaireBrut|number_format(0, ',', ' ') }}€
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-warning rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-calendar-times fa-3x text-white"></i>
                            <div class="ms-3 text-white">
                                <p class="mb-2">Leave Days</p>
                                <h6 class="mb-0">{{ user.soldeConge ?? 0 }}</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-info rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-user-check fa-3x text-white"></i>
                            <div class="ms-3 text-white">
                                <p class="mb-2">Status</p>
                                <h6 class="mb-0">
                                    {% if user.dateFinPeriodeEssai and user.dateFinPeriodeEssai > "now"|date %}
                                        Trial
                                    {% else %}
                                        Active
                                    {% endif %}
                                </h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
{% endblock %}
