{% extends 'base.html.twig' %}

{% block title %}Employee List{% endblock %}

{% block body %}
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Sidebar Start -->
        <div class="sidebar pe-4 pb-3">
            <nav class="navbar bg-light navbar-light">
                <a href="{{ path('employee_dashboard') }}" class="navbar-brand mx-4 mb-3">
                    <h4 class="text-primary"><i class="fa fa-hashtag me-2"></i>RhManagement</h4>
                </a>
                <div class="d-flex align-items-center ms-4 mb-4">
                    <div class="position-relative">
                        <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                        <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
                    </div>
                    <div class="ms-3">
                        <h6 class="mb-0">{{ app.user ? app.user.email : 'Employee' }}</h6>
                        {% set role = app.user ? app.user.roles[0] : null %}
                        <span class="small text-muted">
                            {% if role == 'ROLE_RH' %}
                                Human Resources
                            {% elseif role == 'ROLE_MANAGER' %}
                                Manager
                            {% elseif role == 'ROLE_EMPLOYE' %}
                                Employee
                            {% else %}
                                User
                            {% endif %}
                        </span>
                    </div>
                </div>

                <div class="navbar-nav w-100">
                    <a href="{{ path('rh_dashboard') }}" class="nav-item nav-link">
                        <i class="fa fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle active" data-bs-toggle="dropdown">
                            <i class="fa fa-users me-2"></i>Employees
                        </a>
                        <div class="dropdown-menu bg-transparent border-0">
                            <a href="{{ path('app_user_new') }}" class="dropdown-item">
                                <i class="fa fa-user-plus me-2"></i>Add Employee
                            </a>
                            <a href="{{ path('app_user_index') }}" class="dropdown-item active">
                                <i class="fa fa-list me-2"></i>Employee List
                            </a>
                        </div>
                    </div>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-clock me-2"></i>Time Tracking
                    </a>
                    <a href="{{ path('app_leave_request_index') }}" class="nav-item nav-link">
                        <i class="fa fa-file-alt me-2"></i>Leave Requests</a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-chart-bar me-2"></i>Reports
                    </a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-cog me-2"></i>Settings
                    </a>
                </div>
            </nav>
        </div>
        <!-- Sidebar End -->

        <!-- Content Start -->
        <div class="content">
            <!-- Navbar Start -->
            <nav class="navbar navbar-expand bg-light navbar-light sticky-top px-4 py-0">
                <a href="{{ path('rh_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
                    <h2 class="text-primary mb-0"><i class="fa fa-hashtag"></i></h2>
                </a>
                <a href="#" class="sidebar-toggler flex-shrink-0">
                    <i class="fa fa-bars"></i>
                </a>
                <form class="d-none d-md-flex ms-4">
                    <input class="form-control border-0" type="search" placeholder="Search employees..." aria-label="Search">
                </form>
                <div class="navbar-nav align-items-center ms-auto">
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-envelope me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Messages</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User" style="width: 40px; height: 40px;">
                                    <div class="ms-2">
                                        <h6 class="fw-normal mb-0">New HR message</h6>
                                        <small>15 minutes ago</small>
                                    </div>
                                </div>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item text-center">See all messages</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-bell me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Notifications</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <h6 class="fw-normal mb-0">New employee added</h6>
                                <small>1 hour ago</small>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item text-center">See all notifications</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <img class="rounded-circle me-lg-2" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                            <span class="d-none d-lg-inline-flex">{{ app.user ? app.user.email : 'Employee' }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">My Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <a href="{{ path('app_logout') }}" class="dropdown-item">Log Out</a>
                        </div>
                    </div>
                </div>
            </nav>
            <!-- Navbar End -->

            <!-- Page Header Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="row g-4">
                    <div class="col-12">
                        <div class="bg-light rounded p-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <h4 class="mb-2 text-primary">
                                        <i class="fa fa-users me-2"></i>Employee Management
                                    </h4>
                                    <nav aria-label="breadcrumb">
                                        <ol class="breadcrumb mb-0">
                                            <li class="breadcrumb-item"><a href="{{ path('rh_dashboard') }}">Dashboard</a></li>
                                            <li class="breadcrumb-item active" aria-current="page">Employee List</li>
                                        </ol>
                                    </nav>
                                </div>
                                <div>
                                    <a href="{{ path('app_user_new') }}" class="btn btn-primary">
                                        <i class="fa fa-plus me-2"></i>Add Employee
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Page Header End -->

            <!-- Statistics Cards Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="row g-4">
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-users fa-3x text-primary"></i>
                            <div class="ms-3">
                                <p class="mb-2">Total Employees</p>
                                <h6 class="mb-0">{{ users|length }}</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-user-check fa-3x text-success"></i>
                            <div class="ms-3">
                                <p class="mb-2">Active</p>
                                <h6 class="mb-0">{{ users|length }}</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-briefcase fa-3x text-warning"></i>
                            <div class="ms-3">
                                <p class="mb-2">Departments</p>
                                <h6 class="mb-0">8</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-xl-3">
                        <div class="bg-light rounded d-flex align-items-center justify-content-between p-4">
                            <i class="fa fa-calendar-times fa-3x text-info"></i>
                            <div class="ms-3">
                                <p class="mb-2">On Leave</p>
                                <h6 class="mb-0">3</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Statistics Cards End -->

            <!-- Employee List Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="row g-4">
                    <div class="col-12">
                        <div class="bg-light rounded h-100 p-4">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h6 class="mb-0">
                                    <i class="fa fa-list me-2"></i>Employee List
                                </h6>
                                <div class="d-flex align-items-center">
                                    <div class="dropdown me-3">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fa fa-filter me-2"></i>Filter
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">All</a></li>
                                            <li><a class="dropdown-item" href="#">Active</a></li>
                                            <li><a class="dropdown-item" href="#">On Leave</a></li>
                                            <li><a class="dropdown-item" href="#">New</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fa fa-sort me-2"></i>Sort
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">Name A-Z</a></li>
                                            <li><a class="dropdown-item" href="#">Name Z-A</a></li>
                                            <li><a class="dropdown-item" href="#">Hire Date</a></li>
                                            <li><a class="dropdown-item" href="#">Department</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            {% if users is empty %}
                                <div class="text-center py-5">
                                    <i class="fa fa-users fa-4x text-muted mb-3"></i>
                                    <h5 class="text-muted">No employees found</h5>
                                    <p class="text-muted">Start by adding your first employee to the system.</p>
                                    <a href="{{ path('app_user_new') }}" class="btn btn-primary">
                                        <i class="fa fa-plus me-2"></i>Add First Employee
                                    </a>
                                </div>
                            {% else %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-primary">
                                        <tr>
                                            <th class="text-center">#</th>
                                            <th><i class="fa fa-user me-2"></i>Employee</th>
                                            <th><i class="fa fa-envelope me-2"></i>Email</th>
                                            <th><i class="fa fa-briefcase me-2"></i>Position</th>
                                            <th><i class="fa fa-calendar me-2"></i>Hire Date</th>
                                            <th><i class="fa fa-dollar-sign me-2"></i>Salary</th>
                                            <th><i class="fa fa-calendar-check me-2"></i>Leave Balance</th>
                                            <th class="text-center"><i class="fa fa-cog me-2"></i>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for user in users %}
                                            <tr>
                                                <td class="text-center">
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 12px;">
                                                        {{ loop.index }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                            {{ user.firstname|first|upper }}{{ user.lastname|first|upper }}
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0">{{ user.firstname }} {{ user.lastname }}</h6>
                                                            <small class="text-muted">ID: {{ user.id }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="text-primary">{{ user.email }}</span>
                                                </td>
                                                <td>
                                                    {% if user.poste %}
                                                        <span class="badge bg-info text-dark">{{ user.poste }}</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">Not defined</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if user.dateEmbauche %}
                                                        <span class="text-success">{{ user.dateEmbauche|date('m/d/Y') }}</span>
                                                    {% else %}
                                                        <span class="text-muted">Not defined</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if user.salaireBrut %}
                                                        <strong class="text-success">${{ user.salaireBrut|number_format(0, ',', ' ') }}</strong>
                                                    {% else %}
                                                        <span class="text-muted">Not defined</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if user.soldeConge is not null %}
                                                        <span class="badge bg-success">{{ user.soldeConge }} days</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">Not defined</span>
                                                    {% endif %}
                                                </td>
                                                <td class="text-center">
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ path('app_user_show', {'id': user.id}) }}"
                                                           class="btn btn-sm btn-outline-info"
                                                           title="View Details"
                                                           data-bs-toggle="tooltip">
                                                            <i class="fa fa-eye"></i>
                                                        </a>
                                                        <a href="{{ path('app_user_edit', {'id': user.id}) }}"
                                                           class="btn btn-sm btn-outline-warning"
                                                           title="Edit"
                                                           data-bs-toggle="tooltip">
                                                            <i class="fa fa-edit"></i>
                                                        </a>
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-danger"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#deleteModal{{ user.id }}"
                                                                title="Delete">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <div class="text-muted">
                                        Showing {{ users|length }} employee(s)
                                    </div>
                                    <nav aria-label="Employee pagination">
                                        <ul class="pagination mb-0">
                                            <li class="page-item disabled">
                                                <span class="page-link">Previous</span>
                                            </li>
                                            <li class="page-item active">
                                                <span class="page-link">1</span>
                                            </li>
                                            <li class="page-item disabled">
                                                <span class="page-link">Next</span>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <!-- Employee List End -->

            <!-- Footer Start -->
            <div class="container-fluid pt-4 px-4">
                <div class="bg-light rounded-top p-4">
                    <div class="row">
                        <div class="col-12 col-sm-6 text-center text-sm-start">
                            &copy; <a href="#" class="text-decoration-none">RhManagement</a>, All Rights Reserved.
                        </div>
                        <div class="col-12 col-sm-6 text-center text-sm-end">
                            <span class="text-muted">Version 1.0 - HR System</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Footer End -->
        </div>
        <!-- Content End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- Delete Confirmation Modals -->
    {% for user in users %}
        <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ user.id }}" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0">
                        <h5 class="modal-title text-danger" id="deleteModalLabel{{ user.id }}">
                            <i class="fa fa-exclamation-triangle me-2"></i>
                            Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-user-times fa-2x"></i>
                            </div>
                            <h6>Delete Employee</h6>
                        </div>
                        <p class="text-center">
                            Are you sure you want to delete employee <strong>{{ user.firstname }} {{ user.lastname }}</strong>?
                        </p>
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This action is irreversible and will delete all associated data.
                        </div>
                    </div>
                    <div class="modal-footer border-0">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fa fa-times me-2"></i>Cancel
                        </button>
                        <form method="post" action="{{ path('app_user_delete', {'id': user.id}) }}" class="d-inline">
                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ user.id) }}">
                            <button type="submit" class="btn btn-danger">
                                <i class="fa fa-trash me-2"></i>Delete Permanently
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    {% endfor %}

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('assets/lib/easing/easing.min.js') }}"></script>
    <script src="{{ asset('assets/lib/waypoints/waypoints.min.js') }}"></script>
    <script src="{{ asset('assets/lib/owlcarousel/owl.carousel.min.js') }}"></script>
    <script src="{{ asset('assets/lib/tempusdominus/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/lib/tempusdominus/js/moment-timezone.min.js') }}"></script>
    <script src="{{ asset('assets/lib/tempusdominus/js/tempusdominus-bootstrap-4.min.js') }}"></script>

    <!-- Template Javascript -->
    <script src="{{ asset('assets/js/main.js') }}"></script>

    <!-- Initialize Tooltips -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
{% endblock %}