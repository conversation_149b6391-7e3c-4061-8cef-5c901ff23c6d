<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class SecurityController extends AbstractController
{
    #[Route(path: '/login', name: 'app_login')]
    public function login(AuthenticationUtils $authenticationUtils): Response
    {
        // get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();
        // last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        if ($error) {
            $this->addFlash('error', 'Invalid credentials. Please try again.');
        }

        return $this->render('security/login.html.twig', [
            'last_username' => $lastUsername,
            'error' => $error,
        ]);
    }

    #[Route(path: '/logout', name: 'app_logout')]
    public function logout(): void
    {
        throw new \LogicException('This method can be blank - it will be intercepted by the logout key on your firewall.');
    }

    #[Route(path: '/redirect-after-login', name: 'redirect_after_login')]
    public function redirectAfterLogin(): RedirectResponse
    {
        $user = $this->getUser();

        if (in_array('ROLE_RH', $user->getRoles(), true)) {
            $this->addFlash('success', 'Welcome HR!');
            return $this->redirectToRoute('rh_dashboard');
        }

        if (in_array('ROLE_MANAGER', $user->getRoles(), true)) {
            $this->addFlash('success', 'Welcome Manager!');
            return $this->redirectToRoute('manager_dashboard');
        }

        if (in_array('ROLE_EMPLOYEE', $user->getRoles(), true)) {
            $this->addFlash('success', 'Welcome Employee!');
            return $this->redirectToRoute('employee_dashboard');
        }

        $this->addFlash('success', 'Welcome!');
        return $this->redirectToRoute('employee_dashboard');
    }
}
