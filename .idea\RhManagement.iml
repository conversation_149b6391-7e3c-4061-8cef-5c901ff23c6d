<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" packagePrefix="App\Tests\" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/collections" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/doctrine-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/doctrine-migrations-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/event-manager" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/instantiator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/migrations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/orm" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/persistence" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/sql-formatter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/masterminds/html5" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpstan/phpdoc-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/link" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/staabm/side-effects-detector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/asset" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/asset-mapper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/browser-kit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/config" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/debug-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dependency-injection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/doctrine-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/doctrine-messenger" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dom-crawler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dotenv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/expression-language" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/flex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/form" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/framework-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-client-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/intl" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/maker-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/messenger" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/monolog-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/monolog-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/notifier" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/options-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/password-hasher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-icu" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php84" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/property-access" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/property-info" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/runtime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-core" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-csrf" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-http" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/serializer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/stimulus-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/stopwatch" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/twig-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/twig-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/ux-turbo" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/web-link" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/web-profiler-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/twig/extra-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/twig/twig" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="sweetalert2" level="application" />
  </component>
</module>