<?php

namespace App\Form;

use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Email;


class UserForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('email', EmailType::class, [
                'label' => 'Email',
                'constraints' => [
                    new NotBlank(['message' => 'Email is required.']),
                    new Email(['message' => 'Please enter a valid email address.']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '<EMAIL>'
                ]
            ])
            ->add('firstname', TextType::class, [
                'label' => 'Prénom',
                'constraints' => [
                    new NotBlank(['message' => 'First name is required.']),
                    new Length([
                        'min' => 2,
                        'max' => 100,
                        'minMessage' => 'First name must be at least {{ limit }} characters.',
                        'maxMessage' => 'First name cannot be longer than {{ limit }} characters.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter first name'
                ]
            ])
            ->add('lastname', TextType::class, [
                'label' => 'Nom',
                'constraints' => [
                    new NotBlank(['message' => 'Last name is required.']),
                    new Length([
                        'min' => 2,
                        'max' => 100,
                        'minMessage' => 'Last name must be at least {{ limit }} characters.',
                        'maxMessage' => 'Last name cannot be longer than {{ limit }} characters.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter last name'
                ]
            ])
            ->add('password', PasswordType::class, [
                'label' => 'Password',
                'required' => !$options['is_edit'],
                'mapped' => false,
                'constraints' => $options['is_edit'] ? [] : [
                    new NotBlank(['message' => 'Password is required.']),
                    new Length([
                        'min' => 6,
                        'minMessage' => 'Password must be at least {{ limit }} characters.',
                        'max' => 4096,
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => $options['is_edit'] ? 'Leave blank to keep current password' : 'Enter password'
                ]
            ])
            ->add('roles', ChoiceType::class, [
                'label' => 'Rôle',
                'choices' => [
                    'Employé' => 'ROLE_EMPLOYEE',
                    'Manager' => 'ROLE_MANAGER',
                    'RH' => 'ROLE_RH'
                ],
                'mapped' => false,
                'data' => $options['is_edit'] ? $options['data']->getRoles()[0] : 'ROLE_EMPLOYEE'
            ]);

        // Add form event listener to handle dynamic manager field
        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) {
            $form = $event->getForm();
            $data = $event->getData();

            $selectedRole = $data['roles'] ?? null;

            // Only add manager field if role is EMPLOYEE or MANAGER
            if ($selectedRole && $selectedRole !== 'ROLE_RH') {
                $form->add('manager', EntityType::class, [
                    'class' => User::class,
                    'choice_label' => function (User $user) {
                        return $user->getFirstname() . ' ' . $user->getLastname();
                    },
                    'query_builder' => function (EntityRepository $er) {
                        return $er->createQueryBuilder('u')
                            ->where('u.roles LIKE :role')
                            ->setParameter('role', '%ROLE_MANAGER%')
                            ->orderBy('u.firstname', 'ASC');
                    },
                    'required' => $selectedRole === 'ROLE_EMPLOYEE',
                    'placeholder' => 'Sélectionnez un manager',
                    'label' => 'Manager'
                ]);
            }
        });

        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
            $form = $event->getForm();
            $user = $event->getData();

            if (!$user) {
                return;
            }

            $role = $user->getRoles()[0] ?? null;

            if ($role && $role !== 'ROLE_RH') {
                $form->add('manager', EntityType::class, [
                    'class' => User::class,
                    'choice_label' => function (User $user) {
                        return $user->getFirstname() . ' ' . $user->getLastname();
                    },
                    'query_builder' => function (EntityRepository $er) {
                        return $er->createQueryBuilder('u')
                            ->where('u.roles LIKE :role')
                            ->setParameter('role', '%ROLE_MANAGER%')
                            ->orderBy('u.firstname', 'ASC');
                    },
                    'required' => $role === 'ROLE_EMPLOYEE',
                    'placeholder' => 'Sélectionnez un manager',
                    'label' => 'Manager'
                ]);
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => User::class,
            'is_edit' => false
        ]);
    }
}
