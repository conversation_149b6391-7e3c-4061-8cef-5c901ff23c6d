<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>{% block title %}New Employee - RhManagement{% endblock %}</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="{{ asset('assets/img/favicon.ico') }}" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="{{ asset('assets/lib/owlcarousel/assets/owl.carousel.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/lib/tempusdominus/css/tempusdominus-bootstrap-4.min.css') }}" rel="stylesheet" />

    <!-- Customized Bootstrap Stylesheet -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="{{ asset('assets/css/style.css') }}" rel="stylesheet">
</head>

<body>
{% block body %}
<div class="container-xxl position-relative bg-white d-flex p-0">
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Sidebar Start -->
    <div class="sidebar pe-4 pb-3">
        <nav class="navbar bg-light navbar-light">
            <a href="{{ path('employee_dashboard') }}" class="navbar-brand mx-4 mb-3">
                <h4 class="text-primary"><i class="fa fa-hashtag me-2"></i>RhManagement</h4>
            </a>
            <div class="d-flex align-items-center ms-4 mb-4">
                <div class="position-relative">
                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                    <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
                </div>
                <div class="ms-3">
                    <h6 class="mb-0">{{ app.user ? app.user.email : 'Employee' }}</h6>
                    {% set role = app.user ? app.user.roles[0] : null %}
                    <span>
                            {% if role == 'ROLE_RH' %}
                                Human Resources
                            {% elseif role == 'ROLE_MANAGER' %}
                                Manager
                            {% elseif role == 'ROLE_EMPLOYE' %}
                                Employee
                            {% else %}
                                User
                            {% endif %}
                        </span>
                </div>
            </div>

            <div class="navbar-nav w-100">
                <a href="{{ path('rh_dashboard') }}" class="nav-item nav-link">
                    <i class="fa fa-tachometer-alt me-2"></i>Dashboard</a>
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle active" data-bs-toggle="dropdown">
                        <i class="fa fa-user me-2"></i>Employees
                    </a>
                    <div class="dropdown-menu bg-transparent border-0 text-center">
                        <a href="{{ path('app_user_new') }}" class="dropdown-item small py-2 active">
                            <i class="fa fa-user-plus me-1"></i> Add Employee
                        </a>
                        <a href="{{ path('app_user_index') }}" class="dropdown-item small py-2">
                            <i class="fa fa-users me-1"></i> Employee List
                        </a>
                    </div>
                </div>
                <a href="#" class="nav-item nav-link">
                    <i class="fa fa-clock me-2"></i>Time Tracking</a>
                <a href="{{ path('app_leave_request_index') }}" class="nav-item nav-link">
                    <i class="fa fa-file-alt me-2"></i>Leave Requests</a>
                <a href="#" class="nav-item nav-link">
                    <i class="fa fa-user me-2"></i>My Profile</a>
                <a href="#" class="nav-item nav-link">
                    <i class="fa fa-envelope me-2"></i>Messages</a>
                <a href="#" class="nav-item nav-link">
                    <i class="fa fa-cog me-2"></i>Settings</a>
            </div>
        </nav>
    </div>
    <!-- Sidebar End -->

    <!-- Content Start -->
    <div class="content">
        <!-- Navbar Start -->
        <nav class="navbar navbar-expand bg-light navbar-light sticky-top px-4 py-0">
            <a href="{{ path('rh_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
                <h2 class="text-primary mb-0"><i class="fa fa-hashtag"></i></h2>
            </a>
            <a href="#" class="sidebar-toggler flex-shrink-0">
                <i class="fa fa-bars"></i>
            </a>
            <form class="d-none d-md-flex ms-4">
                <input class="form-control border-0" type="search" placeholder="Search" aria-label="Search">
            </form>
            <div class="navbar-nav align-items-center ms-auto">
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fa fa-envelope me-lg-2"></i>
                        <span class="d-none d-lg-inline-flex">Messages</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                        <a href="#" class="dropdown-item">
                            <div class="d-flex align-items-center">
                                <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User" style="width: 40px; height: 40px;">
                                <div class="ms-2">
                                    <h6 class="fw-normal mb-0">New employee request</h6>
                                    <small>15 minutes ago</small>
                                </div>
                            </div>
                        </a>
                        <hr class="dropdown-divider">
                        <a href="#" class="dropdown-item text-center">See all messages</a>
                    </div>
                </div>
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fa fa-bell me-lg-2"></i>
                        <span class="d-none d-lg-inline-flex">Notifications</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                        <a href="#" class="dropdown-item">
                            <h6 class="fw-normal mb-0">New employee added</h6>
                            <small>15 minutes ago</small>
                        </a>
                        <hr class="dropdown-divider">
                        <a href="#" class="dropdown-item text-center">See all notifications</a>
                    </div>
                </div>
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                        <img class="rounded-circle me-lg-2" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                        <span class="d-none d-lg-inline-flex">{{ app.user ? app.user.email : 'Employee' }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                        <a href="#" class="dropdown-item">My Profile</a>
                        <a href="#" class="dropdown-item">Settings</a>
                        <a href="{{ path('app_logout') }}" class="dropdown-item">Log Out</a>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Navbar End -->

        <!-- Page Header Start -->
        <div class="container-fluid pt-4 px-4">
            <div class="bg-light rounded p-4 mb-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h1 class="mb-0 text-primary">
                            <i class="fa fa-user-plus me-2"></i>Add New Employee
                        </h1>
                        <p class="text-muted mb-0">Fill in the form below to create a new employee account</p>
                    </div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ path('rh_dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ path('app_user_index') }}">Employees</a></li>
                            <li class="breadcrumb-item active" aria-current="page">New Employee</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
        <!-- Page Header End -->

        <!-- Form Start -->
        <div class="container-fluid pt-4 px-4">
            <div class="row g-4">
                <div class="col-12">
                    <div class="bg-light rounded p-4">
                        <div class="row">
                            <div class="col-md-8 mx-auto">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fa fa-user-circle me-2"></i>Employee Information
                                        </h5>
                                    </div>
                                    <div class="card-body p-4">
                                        {{ include('user/_form.html.twig') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Form End -->

        <!-- Action Buttons Start -->
        <div class="container-fluid pt-4 px-4">
            <div class="row g-4">
                <div class="col-12">
                    <div class="bg-light rounded p-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ path('app_user_index') }}" class="btn btn-outline-secondary">
                                <i class="fa fa-arrow-left me-2"></i>Back to Employee List
                            </a>
                            <div class="text-muted">
                                <small>
                                    <i class="fa fa-info-circle me-1"></i>
                                    All fields marked with (*) are required
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Action Buttons End -->

        <!-- Help Section Start -->
        <div class="container-fluid pt-4 px-4">
            <div class="row g-4">
                <div class="col-12">
                    <div class="bg-light rounded p-4">
                        <h6 class="text-primary mb-3">
                            <i class="fa fa-question-circle me-2"></i>Need Help?
                        </h6>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <i class="fa fa-shield-alt text-info me-3"></i>
                                    <div>
                                        <h6 class="mb-0">Role Assignment</h6>
                                        <small class="text-muted">Select appropriate role for access control</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <i class="fa fa-key text-warning me-3"></i>
                                    <div>
                                        <h6 class="mb-0">Password Security</h6>
                                        <small class="text-muted">Use strong passwords (8+ chars, mixed case)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <i class="fa fa-envelope text-success me-3"></i>
                                    <div>
                                        <h6 class="mb-0">Email Verification</h6>
                                        <small class="text-muted">Ensure valid email for notifications</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Help Section End -->

        <!-- Footer Start -->
        <div class="container-fluid pt-4 px-4">
            <div class="bg-light rounded-top p-4">
                <div class="row">
                    <div class="col-12 col-sm-6 text-center text-sm-start">
                        &copy; <a href="#">RhManagement</a>, All Right Reserved.
                    </div>
                    <div class="col-12 col-sm-6 text-center text-sm-end">
                        HR Management System v1.0
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer End -->
    </div>
    <!-- Content End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
</div>
{% endblock %}

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ asset('assets/lib/easing/easing.min.js') }}"></script>
<script src="{{ asset('assets/lib/waypoints/waypoints.min.js') }}"></script>
<script src="{{ asset('assets/lib/owlcarousel/owl.carousel.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/moment.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/moment-timezone.min.js') }}"></script>
<script src="{{ asset('assets/lib/tempusdominus/js/tempusdominus-bootstrap-4.min.js') }}"></script>

<!-- Template Javascript -->
<script src="{{ asset('assets/js/main.js') }}"></script>

<!-- Form Enhancement Script -->
<script>
    // Add form validation feedback
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const spinner = document.getElementById('spinner');
                if (spinner) {
                    spinner.style.display = 'flex';
                }
            });
        });

        // Add floating labels effect
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    });
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const rolesField = document.querySelector('[name$="[roles]"]');
    const managerField = document.querySelector('[name$="[manager]"]').closest('.form-group');

    function toggleManagerField() {
        const selectedRole = rolesField.value;
        if (selectedRole === 'ROLE_RH') {
            managerField.style.display = 'none';
        } else {
            managerField.style.display = 'block';
            managerField.querySelector('select').required = (selectedRole === 'ROLE_EMPLOYEE');
        }
    }

    rolesField.addEventListener('change', toggleManagerField);
    toggleManagerField();
});
</script>
</body>

</html>