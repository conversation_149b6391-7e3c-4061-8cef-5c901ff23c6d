{% extends 'base.html.twig' %}

{% block title %}Mes demandes de congé{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Mes demandes de congé</h1>
                <div>
                    <span class="badge bg-info me-2">Solde: {{ user.soldeConge ?? 0 }} jours</span>
                    <a href="{{ path('app_leave_request_new') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Nouvelle demande
                    </a>
                </div>
            </div>

            {% if leave_requests is empty %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Vous n'avez encore fait aucune demande de congé.
                    <a href="{{ path('app_leave_request_new') }}" class="alert-link">Créer votre première demande</a>
                </div>
            {% else %}
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Période</th>
                                        <th>Jours</th>
                                        <th>Statut</th>
                                        <th>Créée le</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for request in leave_requests %}
                                        <tr>
                                            <td>
                                                {% if request.type == 'annual' %}
                                                    <span class="badge bg-primary">Congé annuel</span>
                                                {% elseif request.type == 'sick' %}
                                                    <span class="badge bg-warning">Maladie</span>
                                                {% elseif request.type == 'maternity' %}
                                                    <span class="badge bg-info">Maternité</span>
                                                {% elseif request.type == 'paternity' %}
                                                    <span class="badge bg-info">Paternité</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Autre</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ request.startDate|date('d/m/Y') }} - {{ request.endDate|date('d/m/Y') }}
                                            </td>
                                            <td>{{ request.daysRequested }}</td>
                                            <td>
                                                {% if request.status == 'pending' %}
                                                    <span class="badge bg-warning">En attente</span>
                                                {% elseif request.status == 'manager_approved' %}
                                                    <span class="badge bg-info">Approuvé par manager</span>
                                                {% elseif request.status == 'manager_rejected' %}
                                                    <span class="badge bg-danger">Refusé par manager</span>
                                                {% elseif request.status == 'hr_approved' %}
                                                    <span class="badge bg-success">Approuvé</span>
                                                {% elseif request.status == 'hr_rejected' %}
                                                    <span class="badge bg-danger">Refusé par RH</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ request.createdAt|date('d/m/Y H:i') }}</td>
                                            <td>
                                                <a href="{{ path('app_leave_request_show', {id: request.id}) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> Voir
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}