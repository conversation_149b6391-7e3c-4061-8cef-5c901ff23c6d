{# filepath: c:\xampp\htdocs\rhmangement\templates\contrat/_form.html.twig #}
{{ form_start(form, {'attr': {'class': 'row g-4'}}) }}
    {% for field in form %}
        <div class="col-md-6">
            <div class="form-floating mb-3">
                {{ form_widget(field, {
                    'attr': {
                        'class': 'form-control rounded-pill shadow-sm',
                        'placeholder': form_label(field)
                    }
                }) }}
                {{ form_label(field, null, {'label_attr': {'class': 'form-label'}}) }}
                {{ form_errors(field) }}
            </div>
        </div>
    {% endfor %}
    <div class="col-12">
        <button class="btn btn-primary px-4 py-2 rounded-pill shadow">Save</button>
    </div>
{{ form_end(form) }}