{% extends 'layout.html.twig' %}

{% block title %}HR Decision{% endblock %}

{% block stylesheets %}
<style>
.decision-card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.decision-card .card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-bottom: none;
}

.info-table td {
    padding: 0.75rem 0.5rem;
    border: none;
}

.info-table td:first-child {
    font-weight: 600;
    color: #495057;
    width: 40%;
}

.decision-section {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.btn-decision {
    min-width: 120px;
    font-weight: 500;
}

.workflow-timeline {
    position: relative;
    padding-left: 30px;
}

.workflow-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}
</style>
{% endblock %}

{% block body %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card decision-card">
            <div class="card-header">
                <h3 class="card-title mb-0"><i class="fas fa-user-tie me-2"></i>HR Final Decision</h3>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-info-circle me-2 text-primary"></i>Request Details</h5>
                        <table class="table info-table">
                            <tr>
                                <td>Employee:</td>
                                <td>{{ leave_request.employee.fullName }}</td>
                            </tr>
                            <tr>
                                <td>Type:</td>
                                <td>
                                    {% if leave_request.type == 'annual' %}
                                        <span class="badge bg-primary">Annual Leave</span>
                                    {% elseif leave_request.type == 'sick' %}
                                        <span class="badge bg-warning">Sick Leave</span>
                                    {% elseif leave_request.type == 'maternity' %}
                                        <span class="badge bg-info">Maternity Leave</span>
                                    {% elseif leave_request.type == 'paternity' %}
                                        <span class="badge bg-info">Paternity Leave</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Other</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td>Period:</td>
                                <td>{{ leave_request.startDate|date('d/m/Y') }} - {{ leave_request.endDate|date('d/m/Y') }}</td>
                            </tr>
                            <tr>
                                <td>Days Requested:</td>
                                <td><span class="badge bg-info fs-6">{{ leave_request.daysRequested }} days</span></td>
                            </tr>
                            <tr>
                                <td>Employee Balance:</td>
                                <td><span class="badge bg-success fs-6">{{ leave_request.employee.soldeConge ?? 0 }} days</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-route me-2 text-primary"></i>Approval Workflow</h5>
                        <div class="workflow-timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6>Request Submitted</h6>
                                    <p class="text-muted mb-0">{{ leave_request.createdAt|date('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                            
                            {% if leave_request.managerDecisionAt %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6>Manager Approved</h6>
                                        <p class="text-muted mb-0">{{ leave_request.managerDecisionAt|date('d/m/Y H:i') }}</p>
                                        {% if leave_request.managerComment %}
                                            <small class="text-muted">"{{ leave_request.managerComment }}"</small>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                            
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6>Pending HR Decision</h6>
                                    <p class="text-muted mb-0">Awaiting your approval</p>
                                </div>
                            </div>
                        </div>
                        
                        {% if leave_request.reason %}
                            <div class="mt-3">
                                <h6><i class="fas fa-comment me-2 text-primary"></i>Employee Reason</h6>
                                <div class="alert alert-light border-start border-primary border-4">
                                    <i class="fas fa-quote-left text-muted me-2"></i>
                                    {{ leave_request.reason }}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="decision-section">
                    <h5><i class="fas fa-gavel me-2 text-primary"></i>HR Final Decision</h5>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> This is the final approval step. Once approved, the leave days will be deducted from the employee's balance.
                    </div>
                    
                    {{ form_start(form) }}
                        <div class="mb-4">
                            {{ form_label(form.decision, 'Decision', {'label_attr': {'class': 'form-label fw-bold'}}) }}
                            <div class="mt-2">
                                {{ form_widget(form.decision, {'attr': {'class': 'form-check-input me-2'}}) }}
                            </div>
                            {{ form_errors(form.decision) }}
                        </div>

                        <div class="mb-4">
                            {{ form_label(form.comment, 'HR Comment (Optional)', {'label_attr': {'class': 'form-label fw-bold'}}) }}
                            {{ form_widget(form.comment, {'attr': {'class': 'form-control', 'rows': 4, 'placeholder': 'Add your HR comments here...'}}) }}
                            {{ form_errors(form.comment) }}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ path('app_leave_request_hr_pending') }}" class="btn btn-secondary btn-decision">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </a>
                            <button type="submit" class="btn btn-success btn-decision">
                                <i class="fas fa-check me-2"></i>Confirm Final Decision
                            </button>
                        </div>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
