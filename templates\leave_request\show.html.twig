{% extends 'layout.html.twig' %}

{% block title %}Leave Request Details{% endblock %}

{% block body %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">Leave Request Details</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>General Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Employee:</strong></td>
                                <td>{{ leave_request.employee.fullName }}</td>
                            </tr>
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td>
                                    {% if leave_request.type == 'annual' %}
                                        <span class="badge bg-primary">Annual Leave</span>
                                    {% elseif leave_request.type == 'sick' %}
                                        <span class="badge bg-warning">Sick Leave</span>
                                    {% elseif leave_request.type == 'maternity' %}
                                        <span class="badge bg-info">Maternity Leave</span>
                                    {% elseif leave_request.type == 'paternity' %}
                                        <span class="badge bg-info">Paternity Leave</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Other</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Period:</strong></td>
                                <td>{{ leave_request.startDate|date('d/m/Y') }} - {{ leave_request.endDate|date('d/m/Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Days Requested:</strong></td>
                                <td>{{ leave_request.daysRequested }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    {% if leave_request.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elseif leave_request.status == 'manager_approved' %}
                                        <span class="badge bg-info">Manager Approved</span>
                                    {% elseif leave_request.status == 'manager_rejected' %}
                                        <span class="badge bg-danger">Manager Rejected</span>
                                    {% elseif leave_request.status == 'hr_approved' %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elseif leave_request.status == 'hr_rejected' %}
                                        <span class="badge bg-danger">HR Rejected</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ leave_request.createdAt|date('d/m/Y H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Approval Workflow</h5>
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6>Request Submitted</h6>
                                    <p class="text-muted">{{ leave_request.createdAt|date('d/m/Y H:i') }}</p>
                                </div>
                            </div>

                            {% if leave_request.managerDecisionAt %}
                                <div class="timeline-item">
                                    <div class="timeline-marker {{ leave_request.status == 'manager_approved' ? 'bg-success' : 'bg-danger' }}"></div>
                                    <div class="timeline-content">
                                        <h6>Manager Decision</h6>
                                        <p class="text-muted">{{ leave_request.managerDecisionAt|date('d/m/Y H:i') }}</p>
                                        <p>{{ leave_request.status == 'manager_approved' ? 'Approved' : 'Rejected' }}</p>
                                    </div>
                                </div>
                            {% endif %}

                            {% if leave_request.hrDecisionAt %}
                                <div class="timeline-item">
                                    <div class="timeline-marker {{ leave_request.status == 'hr_approved' ? 'bg-success' : 'bg-danger' }}"></div>
                                    <div class="timeline-content">
                                        <h6>HR Decision</h6>
                                        <p class="text-muted">{{ leave_request.hrDecisionAt|date('d/m/Y H:i') }}</p>
                                        <p>{{ leave_request.status == 'hr_approved' ? 'Approved' : 'Rejected' }}</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                {% if leave_request.reason %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Reason</h5>
                            <div class="alert alert-light">
                                {{ leave_request.reason }}
                            </div>
                        </div>
                    </div>
                {% endif %}

                {% if leave_request.managerComment %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Manager Comment</h5>
                            <div class="alert alert-info">
                                {{ leave_request.managerComment }}
                            </div>
                        </div>
                    </div>
                {% endif %}

                {% if leave_request.hrComment %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>HR Comment</h5>
                            <div class="alert alert-info">
                                {{ leave_request.hrComment }}
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ back_url }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back
                </a>

                {% if app.user.hasRole('ROLE_MANAGER') and leave_request.manager == app.user and leave_request.isPending %}
                    <a href="{{ path('app_leave_request_manager_decision', {id: leave_request.id}) }}" class="btn btn-primary ms-2">
                        <i class="fas fa-gavel"></i> Make Decision
                    </a>
                {% endif %}

                {% if app.user.hasRole('ROLE_RH') and leave_request.isManagerApproved %}
                    <a href="{{ path('app_leave_request_hr_decision', {id: leave_request.id}) }}" class="btn btn-primary ms-2">
                        <i class="fas fa-gavel"></i> HR Final Decision
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-content p {
    margin-bottom: 5px;
}
</style>
{% endblock %}