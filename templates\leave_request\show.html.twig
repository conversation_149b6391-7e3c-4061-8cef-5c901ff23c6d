{% extends 'base.html.twig' %}

{% block title %}Détails de la demande de congé{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Détails de la demande de congé</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Informations générales</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Employé:</strong></td>
                                    <td>{{ leave_request.employee.fullName }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Type:</strong></td>
                                    <td>
                                        {% if leave_request.type == 'annual' %}
                                            Congé annuel
                                        {% elseif leave_request.type == 'sick' %}
                                            Congé maladie
                                        {% elseif leave_request.type == 'maternity' %}
                                            Congé maternité
                                        {% elseif leave_request.type == 'paternity' %}
                                            Congé paternité
                                        {% else %}
                                            Autre
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Période:</strong></td>
                                    <td>{{ leave_request.startDate|date('d/m/Y') }} - {{ leave_request.endDate|date('d/m/Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Nombre de jours:</strong></td>
                                    <td>{{ leave_request.daysRequested }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Statut:</strong></td>
                                    <td>
                                        {% if leave_request.status == 'pending' %}
                                            <span class="badge bg-warning">En attente</span>
                                        {% elseif leave_request.status == 'manager_approved' %}
                                            <span class="badge bg-info">Approuvé par manager</span>
                                        {% elseif leave_request.status == 'manager_rejected' %}
                                            <span class="badge bg-danger">Refusé par manager</span>
                                        {% elseif leave_request.status == 'hr_approved' %}
                                            <span class="badge bg-success">Approuvé</span>
                                        {% elseif leave_request.status == 'hr_rejected' %}
                                            <span class="badge bg-danger">Refusé par RH</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Créée le:</strong></td>
                                    <td>{{ leave_request.createdAt|date('d/m/Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Workflow de validation</h5>
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6>Demande soumise</h6>
                                        <p class="text-muted">{{ leave_request.createdAt|date('d/m/Y H:i') }}</p>
                                    </div>
                                </div>
                                
                                {% if leave_request.managerDecisionAt %}
                                    <div class="timeline-item">
                                        <div class="timeline-marker {{ leave_request.status == 'manager_approved' ? 'bg-success' : 'bg-danger' }}"></div>
                                        <div class="timeline-content">
                                            <h6>Décision du manager</h6>
                                            <p class="text-muted">{{ leave_request.managerDecisionAt|date('d/m/Y H:i') }}</p>
                                            <p>{{ leave_request.status == 'manager_approved' ? 'Approuvé' : 'Refusé' }}</p>
                                        </div>
                                    </div>
                                {% endif %}
                                
                                {% if leave_request.hrDecisionAt %}
                                    <div class="timeline-item">
                                        <div class="timeline-marker {{ leave_request.status == 'hr_approved' ? 'bg-success' : 'bg-danger' }}"></div>
                                        <div class="timeline-content">
                                            <h6>Décision RH</h6>
                                            <p class="text-muted">{{ leave_request.hrDecisionAt|date('d/m/Y H:i') }}</p>
                                            <p>{{ leave_request.status == 'hr_approved' ? 'Approuvé' : 'Refusé' }}</p>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if leave_request.reason %}
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Motif</h5>
                                <div class="alert alert-light">
                                    {{ leave_request.reason }}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    {% if leave_request.managerComment %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <h5>Commentaire du manager</h5>
                                <div class="alert alert-info">
                                    {{ leave_request.managerComment }}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    {% if leave_request.hrComment %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <h5>Commentaire RH</h5>
                                <div class="alert alert-info">
                                    {{ leave_request.hrComment }}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ back_url }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>

                    {% if app.user.hasRole('ROLE_MANAGER') and leave_request.manager == app.user and leave_request.isPending %}
                        <a href="{{ path('app_leave_request_manager_decision', {id: leave_request.id}) }}" class="btn btn-primary ms-2">
                            <i class="fas fa-gavel"></i> Prendre une décision
                        </a>
                    {% endif %}

                    {% if app.user.hasRole('ROLE_RH') and leave_request.isManagerApproved %}
                        <a href="{{ path('app_leave_request_hr_decision', {id: leave_request.id}) }}" class="btn btn-primary ms-2">
                            <i class="fas fa-gavel"></i> Décision finale RH
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-content p {
    margin-bottom: 5px;
}
</style>
{% endblock %}