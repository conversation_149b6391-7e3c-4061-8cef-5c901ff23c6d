{% extends 'base.html.twig' %}

{% block title %}Nouvelle demande de congé{% endblock %}

{% block stylesheets %}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .leave-balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(135deg, #2193b0 0%, #6dd5ed 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .form-body {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #2193b0;
            box-shadow: 0 0 0 0.2rem rgba(33, 147, 176, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2193b0 0%, #6dd5ed 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 147, 176, 0.4);
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .days-calculator {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            border-left: 4px solid #2193b0;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
        }
        
        .warning-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        .warning-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .type-icons {
            font-size: 1.2rem;
            margin-right: 10px;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .is-invalid {
            border-color: #dc3545 !important;
        }

        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container mt-4 fade-in">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <!-- Flash Messages -->
            {% for type, messages in app.flashes %}
                {% for message in messages %}
                    <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endfor %}

            <!-- Leave Balance Card -->
            <div class="leave-balance-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-1"><i class="fas fa-calendar-check me-2"></i>Solde de congés</h4>
                        <p class="mb-0">Votre solde disponible pour les demandes de congés</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="fs-2 fw-bold">
                            <span id="leave-balance">{{ current_balance }}</span> jours
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Form Container -->
            <div class="form-container">
                {% if not has_manager %}
                    <div class="warning-container">
                        <div class="warning-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3>Manager requis</h3>
                        <p class="mb-4">Vous ne pouvez pas créer de demande de congé car aucun manager ne vous est assigné.</p>
                        
                        <div class="text-start mt-4">
                            <h5>Pour résoudre ce problème :</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check me-2"></i>Contactez le service RH</li>
                                <li><i class="fas fa-check me-2"></i>Demandez l'assignation d'un manager à votre profil</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <a href="{{ path('app_leave_request_index') }}" class="btn btn-light btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Retour aux demandes
                            </a>
                        </div>
                    </div>
                {% else %}
                    <div class="form-header">
                        <h3 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            Nouvelle demande de congé
                        </h3>
                    </div>
                    
                    <div class="form-body">
                        {{ form_start(form, {'attr': {'id': 'leaveRequestForm', 'novalidate': 'novalidate'}}) }}
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.type, null, {'label_attr': {'class': 'form-label'}}) }}
                                        <i class="fas fa-tags type-icons"></i>
                                        {{ form_widget(form.type, {'attr': {'class': 'form-select', 'id': 'leaveType'}}) }}
                                        {{ form_errors(form.type) }}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-info-circle type-icons"></i>Information
                                        </label>
                                        <div class="form-control-plaintext" id="leaveTypeInfo" style="min-height: 48px; display: flex; align-items: center; color: #6c757d; font-style: italic;">
                                            Sélectionnez un type pour voir les informations
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.startDate, null, {'label_attr': {'class': 'form-label'}}) }}
                                        <i class="fas fa-calendar-alt type-icons"></i>
                                        {{ form_widget(form.startDate, {'attr': {'class': 'form-control', 'id': 'startDate'}}) }}
                                        {{ form_errors(form.startDate) }}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.endDate, null, {'label_attr': {'class': 'form-label'}}) }}
                                        <i class="fas fa-calendar-alt type-icons"></i>
                                        {{ form_widget(form.endDate, {'attr': {'class': 'form-control', 'id': 'endDate'}}) }}
                                        {{ form_errors(form.endDate) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                {{ form_label(form.reason, null, {'label_attr': {'class': 'form-label'}}) }}
                                <i class="fas fa-comment type-icons"></i>
                                {{ form_widget(form.reason, {'attr': {'class': 'form-control', 'id': 'reason'}}) }}
                                {{ form_errors(form.reason) }}
                            </div>

                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <a href="{{ path('app_leave_request_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Retour
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Enregistrer
                                </button>
                            </div>
                        {{ form_end(form) }}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Leave type information
        const leaveTypeInfo = {
            annual: "Congés payés annuels selon votre contrat",
            sick: "Congé pour maladie (justificatif médical requis)",
            maternity: "Congé maternité (16 semaines minimum)",
            paternity: "Congé paternité (25 jours maximum)",
            other: "Autre type de congé (préciser le motif)"
        };

        // Set minimum date to tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const startDateInput = document.getElementById('startDate');
        if (startDateInput) {
            startDateInput.min = tomorrow.toISOString().split('T')[0];
        }

        // Handle leave type selection
        const leaveTypeSelect = document.getElementById('leaveType');
        if (leaveTypeSelect) {
            leaveTypeSelect.addEventListener('change', function() {
                const infoDiv = document.getElementById('leaveTypeInfo');
                const selectedType = this.value;
                
                if (selectedType && leaveTypeInfo[selectedType]) {
                    infoDiv.textContent = leaveTypeInfo[selectedType];
                    infoDiv.style.color = '#495057';
                    infoDiv.style.fontStyle = 'normal';
                } else {
                    infoDiv.textContent = 'Sélectionnez un type pour voir les informations';
                    infoDiv.style.color = '#6c757d';
                    infoDiv.style.fontStyle = 'italic';
                }
            });
        }

        // Handle date changes
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');

        if (startDate) {
            startDate.addEventListener('change', function() {
                const startDateValue = new Date(this.value);
                const endDateInput = document.getElementById('endDate');
                
                // Set minimum end date to start date
                endDateInput.min = this.value;
                
                // Clear end date if it's before start date
                if (endDateInput.value && new Date(endDateInput.value) < startDateValue) {
                    endDateInput.value = '';
                }
                
                calculateDays();
            });
        }

        if (endDate) {
            endDate.addEventListener('change', calculateDays);
        }

        // Calculate working days
        function calculateDays() {
            const startDateValue = document.getElementById('startDate').value;
            const endDateValue = document.getElementById('endDate').value;
            
            if (!startDateValue || !endDateValue) {
                document.getElementById('daysCalculator').style.display = 'none';
                return;
            }
            
            const start = new Date(startDateValue);
            const end = new Date(endDateValue);
            
            if (end < start) {
                document.getElementById('daysCalculator').style.display = 'none';
                return;
            }
            
            let days = 0;
            const current = new Date(start);
            
            while (current <= end) {
                // Skip weekends (Saturday = 6, Sunday = 0)
                if (current.getDay() !== 0 && current.getDay() !== 6) {
                    days++;
                }
                current.setDate(current.getDate() + 1);
            }
            
            document.getElementById('calculatedDays').textContent = days;
            document.getElementById('daysCalculator').style.display = 'block';
            
            // Check balance warning
            const currentBalance = parseInt(document.getElementById('leave-balance').textContent);
            const warningDiv = document.getElementById('balanceWarning');
            
            if (days > currentBalance) {
                warningDiv.style.display = 'block';
            } else {
                warningDiv.style.display = 'none';
            }
        }

        // Form submission handling
        const form = document.getElementById('leaveRequestForm');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';
                submitBtn.disabled = true;
                
                // Re-enable button after a delay if form submission fails
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            });
        }
    </script>
{% endblock %}


