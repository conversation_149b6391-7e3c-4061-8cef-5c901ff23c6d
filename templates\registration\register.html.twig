{% extends 'base.html.twig' %}

{% block title %}Register{% endblock %}

{% block body %}
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <!-- Spinner -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>

        <!-- Register -->
        <div class="container-fluid px-5">
            <div class="row h-200 align-items-center justify-content-center" style="min-height: 100vh;">
                <div class="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
                    <div class="bg-light rounded p-4 p-sm-5 my-4 mx-3">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <h3 class="text-primary me-3"><i class="fa fa-hashtag me-2"></i>RhManagement</h3>
                            <h3>Register</h3>
                        </div>

                        {% if form_errors(registrationForm) %}
                            <div class="alert alert-danger">
                                {{ form_errors(registrationForm) }}
                            </div>
                        {% endif %}

                        {{ form_start(registrationForm) }}
                        <div class="form-floating mb-3">
                            {{ form_widget(registrationForm.email, {'attr': {'class': 'form-control', 'placeholder': '<EMAIL>'}}) }}
                            {{ form_label(registrationForm.email, 'Email address') }}
                        </div>

                        <div class="form-floating mb-3">
                            {{ form_widget(registrationForm.plainPassword, {'attr': {'class': 'form-control', 'placeholder': 'Password'}}) }}
                            {{ form_label(registrationForm.plainPassword, 'Password') }}
                        </div>

                        <div class="form-check mb-4">
                            {{ form_widget(registrationForm.agreeTerms, {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(registrationForm.agreeTerms, 'I agree to the terms and conditions', {'attr': {'class': 'form-check-label'}}) }}
                        </div>

                        <button type="submit" class="btn btn-primary py-3 w-100 mb-4">Register</button>
                        {{ form_end(registrationForm) }}

                        <div class="text-center">
                            <p class="mb-0">Already have an account? <a href="{{ path('app_login') }}">Sign In</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}