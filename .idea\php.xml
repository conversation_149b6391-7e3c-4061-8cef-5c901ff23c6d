<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/config" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/dependency-injection" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/symfony/dotenv" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/filesystem" />
      <path value="$PROJECT_DIR$/vendor/symfony/framework-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/flex" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/runtime" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/symfony/stopwatch" />
      <path value="$PROJECT_DIR$/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/vendor/doctrine/persistence" />
      <path value="$PROJECT_DIR$/vendor/doctrine/migrations" />
      <path value="$PROJECT_DIR$/vendor/symfony/doctrine-bridge" />
      <path value="$PROJECT_DIR$/vendor/doctrine/sql-formatter" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php84" />
      <path value="$PROJECT_DIR$/vendor/doctrine/doctrine-bundle" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/doctrine/collections" />
      <path value="$PROJECT_DIR$/vendor/doctrine/doctrine-migrations-bundle" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/masterminds/html5" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/twig/twig" />
      <path value="$PROJECT_DIR$/vendor/twig/extra-bundle" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/vendor/psr/link" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/symfony/security-bundle" />
      <path value="$PROJECT_DIR$/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/vendor/symfony/validator" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/dom-crawler" />
      <path value="$PROJECT_DIR$/vendor/symfony/browser-kit" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/monolog-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/vendor/symfony/options-resolver" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-icu" />
      <path value="$PROJECT_DIR$/vendor/symfony/twig-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/asset-mapper" />
      <path value="$PROJECT_DIR$/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/vendor/symfony/doctrine-messenger" />
      <path value="$PROJECT_DIR$/vendor/symfony/security-csrf" />
      <path value="$PROJECT_DIR$/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/maker-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/expression-language" />
      <path value="$PROJECT_DIR$/vendor/symfony/security-http" />
      <path value="$PROJECT_DIR$/vendor/symfony/security-core" />
      <path value="$PROJECT_DIR$/vendor/symfony/web-profiler-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/web-link" />
      <path value="$PROJECT_DIR$/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/symfony/stimulus-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/ux-turbo" />
      <path value="$PROJECT_DIR$/vendor/symfony/notifier" />
      <path value="$PROJECT_DIR$/vendor/symfony/debug-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/vendor/symfony/intl" />
      <path value="$PROJECT_DIR$/vendor/symfony/password-hasher" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/vendor/symfony/twig-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/messenger" />
      <path value="$PROJECT_DIR$/vendor/symfony/form" />
      <path value="$PROJECT_DIR$/vendor/symfony/monolog-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/asset" />
      <path value="$PROJECT_DIR$/vendor/doctrine/orm" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/staabm/side-effects-detector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/vendor/doctrine/data-fixtures" />
      <path value="$PROJECT_DIR$/vendor/doctrine/doctrine-fixtures-bundle" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.1" />
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings custom_loader_path="$PROJECT_DIR$/vendor/autoload.php" />
    </phpunit_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>