<?php

namespace App\Tests\Service;

use App\Entity\LeaveRequest;
use App\Entity\Notification;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\NotificationService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

class NotificationServiceTest extends TestCase
{
    private NotificationService $notificationService;
    private EntityManagerInterface|MockObject $entityManager;
    private UserRepository|MockObject $userRepository;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->userRepository = $this->createMock(UserRepository::class);
        
        $this->notificationService = new NotificationService(
            $this->entityManager,
            $this->userRepository
        );
    }

    public function testNotifyManagerDecisionApproved(): void
    {
        // Create test data
        $employee = new User();
        $employee->setEmail('<EMAIL>');
        $employee->setFirstName('John');
        $employee->setLastName('Doe');

        $manager = new User();
        $manager->setEmail('<EMAIL>');
        $manager->setFirstName('Jane');
        $manager->setLastName('Manager');

        $hrUser = new User();
        $hrUser->setEmail('<EMAIL>');
        $hrUser->setFirstName('HR');
        $hrUser->setLastName('User');

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setManager($manager);
        $leaveRequest->setStartDate(new \DateTime('2025-08-01'));
        $leaveRequest->setEndDate(new \DateTime('2025-08-05'));
        $leaveRequest->setDaysRequested(5);

        // Mock HR users repository call
        $this->userRepository
            ->expects($this->once())
            ->method('findByRole')
            ->with('ROLE_RH')
            ->willReturn([$hrUser]);

        // Expect 3 persist calls: employee notification + HR notification
        $this->entityManager
            ->expects($this->exactly(2))
            ->method('persist')
            ->with($this->isInstanceOf(Notification::class));

        $this->entityManager
            ->expects($this->once())
            ->method('flush');

        // Test the method
        $this->notificationService->notifyManagerDecision($leaveRequest, true);
    }

    public function testNotifyManagerDecisionRejected(): void
    {
        // Create test data
        $employee = new User();
        $employee->setEmail('<EMAIL>');
        $employee->setFirstName('John');
        $employee->setLastName('Doe');

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);

        // For rejected requests, no HR notification should be sent
        $this->userRepository
            ->expects($this->never())
            ->method('findByRole');

        // Expect only 1 persist call: employee notification
        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->with($this->isInstanceOf(Notification::class));

        $this->entityManager
            ->expects($this->once())
            ->method('flush');

        // Test the method
        $this->notificationService->notifyManagerDecision($leaveRequest, false);
    }

    public function testNotifyHRDecisionApproved(): void
    {
        // Create test data
        $employee = new User();
        $employee->setEmail('<EMAIL>');
        $employee->setFirstName('John');
        $employee->setLastName('Doe');

        $manager = new User();
        $manager->setEmail('<EMAIL>');
        $manager->setFirstName('Jane');
        $manager->setLastName('Manager');

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setManager($manager);

        // Expect 2 persist calls: employee notification + manager notification
        $this->entityManager
            ->expects($this->exactly(2))
            ->method('persist')
            ->with($this->isInstanceOf(Notification::class));

        $this->entityManager
            ->expects($this->once())
            ->method('flush');

        // Test the method
        $this->notificationService->notifyHRDecision($leaveRequest, true);
    }

    public function testNotifyHRDecisionRejected(): void
    {
        // Create test data
        $employee = new User();
        $employee->setEmail('<EMAIL>');
        $employee->setFirstName('John');
        $employee->setLastName('Doe');

        $manager = new User();
        $manager->setEmail('<EMAIL>');
        $manager->setFirstName('Jane');
        $manager->setLastName('Manager');

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setManager($manager);

        // Expect 2 persist calls: employee notification + manager notification
        $this->entityManager
            ->expects($this->exactly(2))
            ->method('persist')
            ->with($this->isInstanceOf(Notification::class));

        $this->entityManager
            ->expects($this->once())
            ->method('flush');

        // Test the method
        $this->notificationService->notifyHRDecision($leaveRequest, false);
    }
}
