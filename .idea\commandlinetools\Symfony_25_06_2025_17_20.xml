<?xml version="1.0" encoding="UTF-8"?>
<framework xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="schemas/frameworkDescriptionVersion1.1.4.xsd" frameworkId="com.symfony" name="Symfony_25/06/2025 17:20" invoke="88d64139-2874-4ab2-8a2d-eeb2249c2391 C:/xampp/htdocs/RhManagement/bin/console" alias="symfony" enabled="true" version="2">
  <command>
    <name>_complete</name>
    <help><![CDATA[Internal command to provide shell completion suggestions<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--shell</td><td>(-s)</td><td>The shell type ("bash", "fish", "zsh")</td></tr> <tr><td>--input</td><td>(-i)</td><td>An array of input tokens (e.g. COMP_WORDS or argv)</td></tr> <tr><td>--current</td><td>(-c)</td><td>The index of the "input" array that the cursor is in (e.g. COMP_CWORD)</td></tr> <tr><td>--api-version</td><td>(-a)</td><td>The API version of the completion script</td></tr> <tr><td>--symfony</td><td>(-S)</td><td>deprecated</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--shell" shortcut="-s" pattern="equals">
        <help><![CDATA[The shell type ("bash", "fish", "zsh")]]></help>
      </option>
      <option name="--input" shortcut="-i" pattern="equals">
        <help><![CDATA[An array of input tokens (e.g. COMP_WORDS or argv)]]></help>
      </option>
      <option name="--current" shortcut="-c" pattern="equals">
        <help><![CDATA[The index of the "input" array that the cursor is in (e.g. COMP_CWORD)]]></help>
      </option>
      <option name="--api-version" shortcut="-a" pattern="equals">
        <help><![CDATA[The API version of the completion script]]></help>
      </option>
      <option name="--symfony" shortcut="-S" pattern="equals">
        <help><![CDATA[deprecated]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>about</name>
    <help><![CDATA[The <b>about</b> command displays information about the current Symfony project.<br> <br> The <b>PHP</b> section displays important configuration that could affect your application. The values might<br> be different between web and CLI.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>completion</name>
    <help><![CDATA[The <b>completion</> command dumps the shell completion script required<br> to use shell autocompletion (currently, bash, fish, zsh completion are supported).<br> <br> <comment>Static installation<br> -------------------</><br> <br> Dump the script to a global completion file and restart your shell:<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console completion | sudo tee /etc/bash_completion.d/console</><br> <br> Or dump the script to a local file and source it:<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console completion > completion.sh</><br> <br> <comment># source the file whenever you use the project</><br> <b>source completion.sh</><br> <br> <comment># or add this line at the end of your "~/.bashrc" file:</><br> <b>source /path/to/completion.sh</><br> <br> <comment>Dynamic installation<br> --------------------</><br> <br> Add this to the end of your shell configuration file (e.g. <b>"~/.bashrc"</>):<br> <br> <b>eval "$(C:\xampp\htdocs\RhManagement\bin\console completion )"</><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--debug</td><td></td><td>Tail the completion debug log</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>shell[=null]</params>
    <optionsBefore>
      <option name="--debug" shortcut="">
        <help><![CDATA[Tail the completion debug log]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>help</name>
    <help><![CDATA[The <b>help</b> command displays help for a given command:<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console help list</b><br> <br> You can also output the help in other formats by using the <comment>--format</comment> option:<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console help --format=xml list</b><br> <br> To display the list of available commands, please use the <b>list</b> command.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--raw</td><td></td><td>To output raw command help</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>command_name[=null]</params>
    <optionsBefore>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command help]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>list</name>
    <help><![CDATA[The <b>list</b> command lists all commands:<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console list</b><br> <br> You can also display the commands for a specific namespace:<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console list test</b><br> <br> You can also output the information in other formats by using the <comment>--format</comment> option:<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console list --format=xml</b><br> <br> It's also possible to get raw list of commands (useful for embedding command runner):<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console list --raw</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--raw</td><td></td><td>To output raw command list</td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--short</td><td></td><td>To skip describing commands' arguments</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>namespace[=null]</params>
    <optionsBefore>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command list]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--short" shortcut="">
        <help><![CDATA[To skip describing commands' arguments]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>assets:install</name>
    <help><![CDATA[The <b>assets:install</b> command installs bundle assets into a given<br> directory (e.g. the <comment>public</comment> directory).<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console assets:install public</b><br> <br> A "bundles" directory will be created inside the target directory and the<br> "Resources/public" directory of each bundle will be copied into it.<br> <br> To create a symlink to each bundle instead of copying its assets, use the<br> <b>--symlink</b> option (will fall back to hard copies when symbolic links aren't possible:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console assets:install public --symlink</b><br> <br> To make symlink relative, add the <b>--relative</b> option:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console assets:install public --symlink --relative</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--symlink</td><td></td><td>Symlink the assets instead of copying them</td></tr> <tr><td>--relative</td><td></td><td>Make relative symlinks</td></tr> <tr><td>--no-cleanup</td><td></td><td>Do not remove the assets of the bundles that no longer exist</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>target[=null]</params>
    <optionsBefore>
      <option name="--symlink" shortcut="">
        <help><![CDATA[Symlink the assets instead of copying them]]></help>
      </option>
      <option name="--relative" shortcut="">
        <help><![CDATA[Make relative symlinks]]></help>
      </option>
      <option name="--no-cleanup" shortcut="">
        <help><![CDATA[Do not remove the assets of the bundles that no longer exist]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:clear</name>
    <help><![CDATA[The <b>cache:clear</b> command clears and warms up the application cache for a given environment<br> and debug mode:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console cache:clear --env=dev</b><br> <b>php C:/xampp/htdocs/RhManagement/bin/console cache:clear --env=prod --no-debug</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--no-warmup</td><td></td><td>Do not warm up the cache</td></tr> <tr><td>--no-optional-warmers</td><td></td><td>Skip optional cache warmers (faster)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--no-warmup" shortcut="">
        <help><![CDATA[Do not warm up the cache]]></help>
      </option>
      <option name="--no-optional-warmers" shortcut="">
        <help><![CDATA[Skip optional cache warmers (faster)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:pool:clear</name>
    <help><![CDATA[The <b>cache:pool:clear</b> command clears the given cache pools or cache pool clearers.<br> <br> C:/xampp/htdocs/RhManagement/bin/console cache:pool:clear <cache pool or clearer 1> [...<cache pool or clearer N>]<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--all</td><td></td><td>Clear all cache pools</td></tr> <tr><td>--exclude</td><td></td><td>A list of cache pools or cache pool clearers to exclude</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>pools[=null]</params>
    <optionsBefore>
      <option name="--all" shortcut="">
        <help><![CDATA[Clear all cache pools]]></help>
      </option>
      <option name="--exclude" shortcut="" pattern="equals">
        <help><![CDATA[A list of cache pools or cache pool clearers to exclude]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:pool:delete</name>
    <help><![CDATA[The <b>cache:pool:delete</b> deletes an item from a given cache pool.<br> <br> C:/xampp/htdocs/RhManagement/bin/console cache:pool:delete <pool> <key><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>pool key</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:pool:invalidate-tags</name>
    <help><![CDATA[The <b>cache:pool:invalidate-tags</b> command invalidates tags from taggable pools. By default, all pools<br> have the passed tags invalidated. Pass <b>--pool=my_pool</b> to invalidate tags on a specific pool.<br> <br> php C:/xampp/htdocs/RhManagement/bin/console cache:pool:invalidate-tags tag1 tag2<br> php C:/xampp/htdocs/RhManagement/bin/console cache:pool:invalidate-tags tag1 tag2 --pool=cache2 --pool=cache1<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--pool</td><td>(-p)</td><td>The pools to invalidate on</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>tags</params>
    <optionsBefore>
      <option name="--pool" shortcut="-p" pattern="equals">
        <help><![CDATA[The pools to invalidate on]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:pool:list</name>
    <help><![CDATA[The <b>cache:pool:list</b> command lists all available cache pools.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:pool:prune</name>
    <help><![CDATA[The <b>cache:pool:prune</b> command deletes all expired items from all pruneable pools.<br> <br> C:/xampp/htdocs/RhManagement/bin/console cache:pool:prune<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:warmup</name>
    <help><![CDATA[The <b>cache:warmup</b> command warms up the cache.<br> <br> Before running this command, the cache must be empty.<br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--no-optional-warmers</td><td></td><td>Skip optional cache warmers (faster)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--no-optional-warmers" shortcut="">
        <help><![CDATA[Skip optional cache warmers (faster)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config:dump-reference</name>
    <help><![CDATA[The <b>config:dump-reference</b> command dumps the default configuration for an<br> extension/bundle.<br> <br> Either the extension alias or bundle name can be used:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console config:dump-reference framework</b><br> <b>php C:/xampp/htdocs/RhManagement/bin/console config:dump-reference FrameworkBundle</b><br> <br> The <b>--format</b> option specifies the format of the configuration,<br> these are "<comment>yaml</comment>", "<comment>xml</comment>".<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console config:dump-reference FrameworkBundle --format=xml</b><br> <br> For dumping a specific option, add its path as second argument (only available for the yaml format):<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console config:dump-reference framework http_client.default_options</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td></td><td>The output format ("yaml", "xml")</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>name[=null] path[=null]</params>
    <optionsBefore>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format ("yaml", "xml")]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>debug:autowiring</name>
    <help><![CDATA[The <b>debug:autowiring</b> command displays the classes and interfaces that<br> you can use as type-hints for autowiring:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:autowiring</b><br> <br> You can also pass a search term to filter the list:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:autowiring log</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--all</td><td></td><td>Show also services that are not aliased</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>search[=null]</params>
    <optionsBefore>
      <option name="--all" shortcut="">
        <help><![CDATA[Show also services that are not aliased]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>debug:config</name>
    <help><![CDATA[The <b>debug:config</b> command dumps the current configuration for an<br> extension/bundle.<br> <br> Either the extension alias or bundle name can be used:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:config framework</b><br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:config FrameworkBundle</b><br> <br> The <b>--format</b> option specifies the format of the configuration,<br> these are "<comment>txt</comment>", "<comment>yaml</comment>", "<comment>json</comment>".<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:config framework --format=json</b><br> <br> For dumping a specific option, add its path as second argument:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:config framework serializer.enabled</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--resolve-env</td><td></td><td>Display resolved environment variable values instead of placeholders</td></tr> <tr><td>--format</td><td></td><td>The output format ("txt", "yaml", "json")</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>name[=null] path[=null]</params>
    <optionsBefore>
      <option name="--resolve-env" shortcut="">
        <help><![CDATA[Display resolved environment variable values instead of placeholders]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format ("txt", "yaml", "json")]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>debug:container</name>
    <help><![CDATA[The <b>debug:container</b> command displays all configured <comment>public</comment> services:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container</b><br> <br> To see deprecations generated during container compilation and cache warmup, use the <b>--deprecations</b> option:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --deprecations</b><br> <br> To get specific information about a service, specify its name:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container validator</b><br> <br> To get specific information about a service including all its arguments, use the <b>--show-arguments</b> flag:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container validator --show-arguments</b><br> <br> To see available types that can be used for autowiring, use the <b>--types</b> flag:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --types</b><br> <br> To see environment variables used by the container, use the <b>--env-vars</b> flag:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --env-vars</b><br> <br> Display a specific environment variable by specifying its name with the <b>--env-var</b> option:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --env-var=APP_ENV</b><br> <br> Use the --tags option to display tagged <comment>public</comment> services grouped by tag:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --tags</b><br> <br> Find all services with a specific tag by specifying the tag name with the <b>--tag</b> option:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --tag=form.type</b><br> <br> Use the <b>--parameters</b> option to display all parameters:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --parameters</b><br> <br> Display a specific parameter by specifying its name with the <b>--parameter</b> option:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --parameter=kernel.debug</b><br> <br> By default, internal services are hidden. You can display them<br> using the <b>--show-hidden</b> flag:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:container --show-hidden</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--show-arguments</td><td></td><td>Show arguments in services</td></tr> <tr><td>--show-hidden</td><td></td><td>Show hidden (internal) services</td></tr> <tr><td>--tag</td><td></td><td>Show all services with a specific tag</td></tr> <tr><td>--tags</td><td></td><td>Display tagged services for an application</td></tr> <tr><td>--parameter</td><td></td><td>Display a specific parameter for an application</td></tr> <tr><td>--parameters</td><td></td><td>Display parameters for an application</td></tr> <tr><td>--types</td><td></td><td>Display types (classes/interfaces) available in the container</td></tr> <tr><td>--env-var</td><td></td><td>Display a specific environment variable used in the container</td></tr> <tr><td>--env-vars</td><td></td><td>Display environment variables used in the container</td></tr> <tr><td>--format</td><td></td><td>The output format ("txt", "xml", "json", "md")</td></tr> <tr><td>--raw</td><td></td><td>To output raw description</td></tr> <tr><td>--deprecations</td><td></td><td>Display deprecations generated when compiling and warming up the container</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>name[=null]</params>
    <optionsBefore>
      <option name="--show-arguments" shortcut="">
        <help><![CDATA[Show arguments in services]]></help>
      </option>
      <option name="--show-hidden" shortcut="">
        <help><![CDATA[Show hidden (internal) services]]></help>
      </option>
      <option name="--tag" shortcut="" pattern="equals">
        <help><![CDATA[Show all services with a specific tag]]></help>
      </option>
      <option name="--tags" shortcut="">
        <help><![CDATA[Display tagged services for an application]]></help>
      </option>
      <option name="--parameter" shortcut="" pattern="equals">
        <help><![CDATA[Display a specific parameter for an application]]></help>
      </option>
      <option name="--parameters" shortcut="">
        <help><![CDATA[Display parameters for an application]]></help>
      </option>
      <option name="--types" shortcut="">
        <help><![CDATA[Display types (classes/interfaces) available in the container]]></help>
      </option>
      <option name="--env-var" shortcut="" pattern="equals">
        <help><![CDATA[Display a specific environment variable used in the container]]></help>
      </option>
      <option name="--env-vars" shortcut="">
        <help><![CDATA[Display environment variables used in the container]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format ("txt", "xml", "json", "md")]]></help>
      </option>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw description]]></help>
      </option>
      <option name="--deprecations" shortcut="">
        <help><![CDATA[Display deprecations generated when compiling and warming up the container]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>debug:dotenv</name>
    <help><![CDATA[The <b>C:/xampp/htdocs/RhManagement/bin/console debug:dotenv</b> command displays all the environment variables configured by dotenv:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:dotenv</b><br> <br> To get specific variables, specify its full or partial name:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:dotenv FOO_BAR</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>filter[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>debug:event-dispatcher</name>
    <help><![CDATA[The <b>debug:event-dispatcher</b> command displays all configured listeners:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:event-dispatcher</b><br> <br> To get specific listeners for an event, specify its name:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:event-dispatcher kernel.request</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--dispatcher</td><td></td><td>To view events of a specific event dispatcher</td></tr> <tr><td>--format</td><td></td><td>The output format ("txt", "xml", "json", "md")</td></tr> <tr><td>--raw</td><td></td><td>To output raw description</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>event[=null]</params>
    <optionsBefore>
      <option name="--dispatcher" shortcut="" pattern="equals">
        <help><![CDATA[To view events of a specific event dispatcher]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format ("txt", "xml", "json", "md")]]></help>
      </option>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw description]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>debug:router</name>
    <help><![CDATA[The <b>debug:router</b> displays the configured routes:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console debug:router</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--show-controllers</td><td></td><td>Show assigned controllers in overview</td></tr> <tr><td>--show-aliases</td><td></td><td>Show aliases in overview</td></tr> <tr><td>--format</td><td></td><td>The output format ("txt", "xml", "json", "md")</td></tr> <tr><td>--raw</td><td></td><td>To output raw route(s)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>name[=null]</params>
    <optionsBefore>
      <option name="--show-controllers" shortcut="">
        <help><![CDATA[Show assigned controllers in overview]]></help>
      </option>
      <option name="--show-aliases" shortcut="">
        <help><![CDATA[Show aliases in overview]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format ("txt", "xml", "json", "md")]]></help>
      </option>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw route(s)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>lint:container</name>
    <help><![CDATA[This command parses service definitions and ensures that injected values match the type declarations of each services' class.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>lint:yaml</name>
    <help><![CDATA[The <b>lint:yaml</b> command lints a YAML file and outputs to STDOUT<br> the first encountered syntax error.<br> <br> You can validates YAML contents passed from STDIN:<br> <br> <b>cat filename | php C:/xampp/htdocs/RhManagement/bin/console lint:yaml -</b><br> <br> You can also validate the syntax of a file:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console lint:yaml filename</b><br> <br> Or of a whole directory:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console lint:yaml dirname</b><br> <b>php C:/xampp/htdocs/RhManagement/bin/console lint:yaml dirname --format=json</b><br> <br> You can also exclude one or more specific files:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console lint:yaml dirname --exclude="dirname/foo.yaml" --exclude="dirname/bar.yaml"</b><br> <br> Or find all files in a bundle:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console lint:yaml @AcmeDemoBundle</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td></td><td>The output format ("txt", "json", "github")</td></tr> <tr><td>--exclude</td><td></td><td>Path(s) to exclude</td></tr> <tr><td>--parse-tags</td><td></td><td>Parse custom tags</td></tr> <tr><td>--no-parse-tags</td><td></td><td>Negate the "--parse-tags" option</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>filename[=null]</params>
    <optionsBefore>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format ("txt", "json", "github")]]></help>
      </option>
      <option name="--exclude" shortcut="" pattern="equals">
        <help><![CDATA[Path(s) to exclude]]></help>
      </option>
      <option name="--parse-tags" shortcut="">
        <help><![CDATA[Parse custom tags]]></help>
      </option>
      <option name="--no-parse-tags" shortcut="">
        <help><![CDATA[Negate the "--parse-tags" option]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>router:match</name>
    <help><![CDATA[The <b>router:match</b> shows which routes match a given request and which don't and for what reason:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console router:match /foo</b><br> <br> or<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console router:match /foo --method POST --scheme https --host symfony.com --verbose</b><br> <br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--method</td><td></td><td>Set the HTTP method</td></tr> <tr><td>--scheme</td><td></td><td>Set the URI scheme (usually http or https)</td></tr> <tr><td>--host</td><td></td><td>Set the URI host</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>path_info</params>
    <optionsBefore>
      <option name="--method" shortcut="" pattern="equals">
        <help><![CDATA[Set the HTTP method]]></help>
      </option>
      <option name="--scheme" shortcut="" pattern="equals">
        <help><![CDATA[Set the URI scheme (usually http or https)]]></help>
      </option>
      <option name="--host" shortcut="" pattern="equals">
        <help><![CDATA[Set the URI host]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>secrets:decrypt-to-local</name>
    <help><![CDATA[The <b>secrets:decrypt-to-local</b> command decrypts all secrets and copies them in the local vault.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:decrypt-to-local</b><br> <br> When the <b>--force</b> option is provided, secrets that already exist in the local vault are overridden.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:decrypt-to-local --force</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Force overriding of secrets that already exist in the local vault</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Force overriding of secrets that already exist in the local vault]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>secrets:encrypt-from-local</name>
    <help><![CDATA[The <b>secrets:encrypt-from-local</b> command encrypts all locally overridden secrets to the vault.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:encrypt-from-local</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>secrets:generate-keys</name>
    <help><![CDATA[The <b>secrets:generate-keys</b> command generates a new encryption key.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:generate-keys</b><br> <br> If encryption keys already exist, the command must be called with<br> the <b>--rotate</b> option in order to override those keys and re-encrypt<br> existing secrets.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:generate-keys --rotate</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--local</td><td>(-l)</td><td>Update the local vault.</td></tr> <tr><td>--rotate</td><td>(-r)</td><td>Re-encrypt existing secrets with the newly generated keys.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--local" shortcut="-l">
        <help><![CDATA[Update the local vault.]]></help>
      </option>
      <option name="--rotate" shortcut="-r">
        <help><![CDATA[Re-encrypt existing secrets with the newly generated keys.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>secrets:list</name>
    <help><![CDATA[The <b>secrets:list</b> command list all stored secrets.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:list</b><br> <br> When the option <b>--reveal</b> is provided, the decrypted secrets are also displayed.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:list --reveal</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--reveal</td><td>(-r)</td><td>Display decrypted values alongside names</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--reveal" shortcut="-r">
        <help><![CDATA[Display decrypted values alongside names]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>secrets:remove</name>
    <help><![CDATA[The <b>secrets:remove</b> command removes a secret from the vault.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:remove <name></b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--local</td><td>(-l)</td><td>Update the local vault.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--local" shortcut="-l">
        <help><![CDATA[Update the local vault.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>secrets:set</name>
    <help><![CDATA[The <b>secrets:set</b> command stores a secret in the vault.<br> <br> <b>C:/xampp/htdocs/RhManagement/bin/console secrets:set <name></b><br> <br> To reference secrets in services.yaml or any other config<br> files, use <b>"%env(<name>)%"</b>.<br> <br> By default, the secret value should be entered interactively.<br> Alternatively, provide a file where to read the secret from:<br> <br> <b>php C:/xampp/htdocs/RhManagement/bin/console secrets:set <name> filename</b><br> <br> Use "-" as a file name to read from STDIN:<br> <br> <b>cat filename | php C:/xampp/htdocs/RhManagement/bin/console secrets:set <name> -</b><br> <br> Use <b>--local</b> to override secrets for local needs.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--local</td><td>(-l)</td><td>Update the local vault.</td></tr> <tr><td>--random</td><td>(-r)</td><td>Generate a random value.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td>(-e)</td><td>The Environment name.</td></tr> <tr><td>--no-debug</td><td></td><td>Switch off debug mode.</td></tr> <tr><td>--profile</td><td></td><td>Enables profiling (requires debug).</td></tr> </table> <br/>]]></help>
    <params>name file[=null]</params>
    <optionsBefore>
      <option name="--local" shortcut="-l">
        <help><![CDATA[Update the local vault.]]></help>
      </option>
      <option name="--random" shortcut="-r" pattern="equals">
        <help><![CDATA[Generate a random value.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="-e" pattern="equals">
        <help><![CDATA[The Environment name.]]></help>
      </option>
      <option name="--no-debug" shortcut="">
        <help><![CDATA[Switch off debug mode.]]></help>
      </option>
      <option name="--profile" shortcut="">
        <help><![CDATA[Enables profiling (requires debug).]]></help>
      </option>
    </optionsBefore>
  </command>
</framework>

