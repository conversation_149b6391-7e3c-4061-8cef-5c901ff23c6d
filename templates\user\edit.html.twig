{% extends 'base.html.twig' %}

{% block title %}Edit Employee{% endblock %}

{% block body %}
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Sidebar Start -->
        <div class="sidebar pe-4 pb-3">
            <nav class="navbar bg-light navbar-light">
                <a href="{{ path('rh_dashboard') }}" class="navbar-brand mx-4 mb-3">
                    <h4 class="text-primary"><i class="fa fa-hashtag me-2"></i>RhManagement</h4>
                </a>
                <div class="d-flex align-items-center ms-4 mb-4">
                    <div class="position-relative">
                        <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                        <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
                    </div>
                    <div class="ms-3">
                        <h6 class="mb-0">{{ app.user ? app.user.email : 'Employee' }}</h6>
                        {% set role = app.user ? app.user.roles[0] : null %}
                        <span>
                            {% if role == 'ROLE_RH' %}
                                Human Resources
                            {% elseif role == 'ROLE_MANAGER' %}
                                Manager
                            {% elseif role == 'ROLE_EMPLOYE' %}
                                Employee
                            {% else %}
                                User
                            {% endif %}
                        </span>
                    </div>
                </div>

                <div class="navbar-nav w-100">
                    <a href="{{ path('rh_dashboard') }}" class="nav-item nav-link">
                        <i class="fa fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle active" data-bs-toggle="dropdown">
                            <i class="fa fa-user me-2"></i>Employees
                        </a>
                        <div class="dropdown-menu bg-transparent border-0 text-center">
                            <a href="{{ path('app_user_new') }}" class="dropdown-item small py-2">
                                <i class="fa fa-user-plus me-1"></i> Add Employee
                            </a>
                            <a href="{{ path('app_user_index') }}" class="dropdown-item small py-2">
                                <i class="fa fa-users me-1"></i> Employee List
                            </a>
                        </div>
                    </div>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-clock me-2"></i>Time Tracking
                    </a>
                   <a href="{{ path('app_leave_request_index') }}" class="nav-item nav-link">
                    <i class="fa fa-file-alt me-2"></i>Leave Requests
                    </a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-user me-2"></i>My Profile
                    </a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-envelope me-2"></i>Messages
                    </a>
                    <a href="#" class="nav-item nav-link">
                        <i class="fa fa-cog me-2"></i>Settings
                    </a>
                </div>
            </nav>
        </div>
        <!-- Sidebar End -->

        <!-- Content Start -->
        <div class="content">
            <!-- Navbar Start -->
            <nav class="navbar navbar-expand bg-light navbar-light sticky-top px-4 py-0">
                <a href="{{ path('rh_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
                    <h2 class="text-primary mb-0"><i class="fa fa-hashtag"></i></h2>
                </a>
                <a href="#" class="sidebar-toggler flex-shrink-0">
                    <i class="fa fa-bars"></i>
                </a>
                <form class="d-none d-md-flex ms-4">
                    <input class="form-control border-0" type="search" placeholder="Search" aria-label="Search">
                </form>
                <div class="navbar-nav align-items-center ms-auto">
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-envelope me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Messages</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle" src="{{ asset('assets/img/user.jpg') }}" alt="User" style="width: 40px; height: 40px;">
                                    <div class="ms-2">
                                        <h6 class="fw-normal mb-0">New schedule assigned</h6>
                                        <small>15 minutes ago</small>
                                    </div>
                                </div>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item text-center">See all messages</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-bell me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Notifications</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <h6 class="fw-normal mb-0">Leave request approved</h6>
                                <small>15 minutes ago</small>
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item text-center">See all notifications</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <img class="rounded-circle me-lg-2" src="{{ asset('assets/img/user.jpg') }}" alt="User Profile" style="width: 40px; height: 40px;">
                            <span class="d-none d-lg-inline-flex">{{ app.user ? app.user.email : 'Employee' }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-light border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">My Profile</a>
                            <a href="#" class="dropdown-item">Settings</a>
                            <a href="{{ path('app_logout') }}" class="dropdown-item">Log Out</a>
                        </div>
                    </div>
                </div>
            </nav>
            <!-- Navbar End -->

            <!-- Page Content Start -->
            <div class="container-fluid pt-4 px-4">
                <!-- Breadcrumb Start -->
                <div class="row g-4">
                    <div class="col-12">
                        <div class="bg-light rounded p-3 mb-4">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{{ path('rh_dashboard') }}" class="text-decoration-none">
                                            <i class="fa fa-home me-1"></i>Dashboard
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{{ path('app_user_index') }}" class="text-decoration-none">
                                            <i class="fa fa-users me-1"></i>Employees
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <i class="fa fa-user-edit me-1"></i>Edit Employee
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
                <!-- Breadcrumb End -->

                <!-- Edit Form Start -->
                <div class="row g-4">
                    <div class="col-12">
                        <div class="bg-light rounded h-100 p-4">
                            <!-- Header Section -->
                            <div class="row mb-4">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                            <i class="fa fa-user-edit text-white"></i>
                                        </div>
                                        <div>
                                            <h4 class="mb-1 text-primary">Edit Employee</h4>
                                            <p class="mb-0 text-muted">{{ user.firstname }} {{ user.lastname }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <a href="{{ path('app_user_show', {'id': user.id}) }}" class="btn btn-outline-info me-2">
                                        <i class="fa fa-eye me-1"></i>View Details
                                    </a>
                                    <a href="{{ path('app_user_index') }}" class="btn btn-outline-secondary">
                                        <i class="fa fa-arrow-left me-1"></i>Back
                                    </a>
                                </div>
                            </div>

                            <!-- Form Card -->
                            <div class="row justify-content-center">
                                <div class="col-lg-10">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-edit me-2"></i>Employee Information
                                            </h5>
                                        </div>
                                        <div class="card-body p-4">
                                            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}

                                            <!-- Personal Information Section -->
                                            <div class="row mb-4">
                                                <div class="col-12">
                                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                                        <i class="fa fa-user me-2"></i>Personal Information
                                                    </h6>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.firstname, {'attr': {'class': 'form-control', 'placeholder': 'First Name'}}) }}
                                                        {{ form_label(form.firstname, 'First Name *', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.firstname) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.lastname, {'attr': {'class': 'form-control', 'placeholder': 'Last Name'}}) }}
                                                        {{ form_label(form.lastname, 'Last Name *', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.lastname) }}
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Email'}}) }}
                                                        {{ form_label(form.email, 'Email *', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.email) }}
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.password, {'attr': {'class': 'form-control', 'placeholder': 'New Password (optional)'}}) }}
                                                        {{ form_label(form.password, 'New Password (optional)', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.password) }}
                                                        <div class="form-text">
                                                            <i class="fa fa-info-circle me-1"></i>
                                                            Leave blank to keep the current password
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Professional Information Section -->
                                            <div class="row mb-4">
                                                <div class="col-12">
                                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                                        <i class="fa fa-briefcase me-2"></i>Professional Information
                                                    </h6>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.poste, {'attr': {'class': 'form-control', 'placeholder': 'Position'}}) }}
                                                        {{ form_label(form.poste, 'Position', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.poste) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.roles, {'attr': {'class': 'form-select'}}) }}
                                                        {{ form_label(form.roles, 'Roles', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.roles) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.dateEmbauche, {'attr': {'class': 'form-control'}}) }}
                                                        {{ form_label(form.dateEmbauche, 'Hire Date', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.dateEmbauche) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.dateFinPeriodeEssai, {'attr': {'class': 'form-control'}}) }}
                                                        {{ form_label(form.dateFinPeriodeEssai, 'End of Probation Period', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.dateFinPeriodeEssai) }}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Financial Information Section -->
                                            <div class="row mb-4">
                                                <div class="col-12">
                                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                                        <i class="fa fa-euro-sign me-2"></i>Financial Information
                                                    </h6>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.salaireBrut, {'attr': {'class': 'form-control', 'placeholder': 'Gross Salary', 'step': '0.01'}}) }}
                                                        {{ form_label(form.salaireBrut, 'Gross Salary (€)', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.salaireBrut) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating mb-3">
                                                        {{ form_widget(form.soldeConge, {'attr': {'class': 'form-control', 'placeholder': 'Leave Balance', 'step': '0.5'}}) }}
                                                        {{ form_label(form.soldeConge, 'Leave Balance (days)', {'label_attr': {'class': 'form-label'}}) }}
                                                        {{ form_errors(form.soldeConge) }}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Action Buttons -->
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="d-flex justify-content-center flex-wrap gap-2">
                                                        <button type="submit" class="btn btn-primary px-4 py-2">
                                                            <i class="fa fa-save me-2"></i>Update
                                                        </button>
                                                        <a href="{{ path('app_user_show', {'id': user.id}) }}" class="btn btn-info px-4 py-2">
                                                            <i class="fa fa-eye me-2"></i>View Details
                                                        </a>
                                                        <a href="{{ path('app_user_index') }}" class="btn btn-secondary px-4 py-2">
                                                            <i class="fa fa-times me-2"></i>Cancel
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>

                                            {{ form_end(form) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Edit Form End -->

                <!-- Footer Start -->
                <div class="container-fluid pt-4 px-4">
                    <div class="bg-light rounded-top p-4">
                        <div class="row">
                            <div class="col-12 col-sm-6 text-center text-sm-start">
                                &copy; <a href="#">RhManagement</a>, All Rights Reserved.
                            </div>
                            <div class="col-12 col-sm-6 text-center text-sm-end">
                                Employee Management System v1.0
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Footer End -->
            </div>
            <!-- Page Content End -->
        </div>
        <!-- Content End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('assets/lib/easing/easing.min.js') }}"></script>
    <script src="{{ asset('assets/lib/waypoints/waypoints.min.js') }}"></script>
    <script src="{{ asset('assets/lib/owlcarousel/owl.carousel.min.js') }}"></script>
    <script src="{{ asset('assets/lib/tempusdominus/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/lib/tempusdominus/js/moment-timezone.min.js') }}"></script>
    <script src="{{ asset('assets/lib/tempusdominus/js/tempusdominus-bootstrap-4.min.js') }}"></script>
    <script src="{{ asset('assets/js/main.js') }}"></script>
{% endblock %}
